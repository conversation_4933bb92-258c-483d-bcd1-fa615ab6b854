# Merchant Integration Interface

## Seamless Integration Guide

## V.5. 49

The information presented herein is confidential information of Skywind Holdings Ltd. It is also protected subject matter of copyrights owned by Skywind
Holdings Ltd. and of agreements between Skywind Holdings Ltd. and its licensees and other parties. Copying, transmission and disclosure of such information
can only be done within the strict scope of a governing Skywind Holdings Ltd. agreement. In the absence of any specific agreement to the contrary, reverse
engineering, decompilation and disassembly are prohibited in any event as to any software content. While all efforts have been made to ensure that the
content of this document is accurate at the time of publication, the data upon which this document is based is subject to future change. Updated versions of
this document will be released, when necessary, resources permitting.

## July 2025


Copyright © 2025 Skywind Holdings Ltd.

## Preface

**Purpose**

The purpose of this document is to provide information on how to successfully

integrate Systems between the merchant and the provider’s platforms.

**Scope**

This document describes the basic API commands and gives instructions for Seamless

Integration.

**Intended Audience**

Licensees of Skywind Holdings Ltd.


## Copyright © 2025 Skywind Holdings Ltd.


- 1. Introduction Table of Contents
      - 1.1. Basic Definitions
      - 1.2. Description
- 2. API Methods
   - 2.1. Validate Ticket
   - 2.2. Refresh Session Token
   - 2.3. Get Balance Operations
      - 2.3.1. Get Balance
   - 2.4. Transactions
      - 2.4.1. Debit Customer
      - 2.4.2. Credit Customer
      - 2.4.3. Rollback (Refund) Customer’s Bet Transaction
   - 2.5. Player Logout
   - 2.6. Get Ticket
- 3. Marketing Tools
   - 3.1. Free Bets...........................................................................................................................
      - 3.1.1. Get Free Bets
      - 3.1.2. Debit Customer with Free Bets..........................................................................................
      - 3.1.3. Credit Customer with Free Bets
   - 3.2. FunBonus Management
      - 3.2.1. FunBonus Overview
      - 3.2.2. FunBonus Wagering Rules
      - 3.2.3. Technical Implementation
   - 3.3. Other Marketing Tools
      - 3.3.1. Bonus Payment (Credit a Customer with a Bonus Win)
- 4. Error Responses
- 5. Falcon API
   - 5.1. Endpoints and Routing
   - 5.2. Parameters Format
   - 5.3. Multi Domain
   - 5.4. Authentication
   - 5 .5. User Login
   - 5.6. Refresh Access Token
   - 5 .7. Get List of Games
      - 5.7.1. Get Jackpot Tickers.............................................................................................................
   - 5.8. Get Game Info
   - 5.9. Get Player’s Game URL
   - 5.10. Get Fun Mode (Anonymous) Game URL
   - 5 .11. Get Game History
      - 5.11.1. Get Game History from 3 rd Party Game Providers
   - 5.12. Get Game History Round Details
      - 5.12.1. Get Game History Round Details from 3 rd Party Game Providers
   - 5.13. Get Game Spin History for the Brand
   - 5.14. Get Game History Round Details SmResult
   - 5.15. Get Game History Round Details Visualisation................................................................
   - 5.16. Get Game Event Details Visualisation
   - 5.17. Get Currency Report.......................................................................................................
   - 5 .18. Get Player Report
   - 5 .19. Get List of Countries
   - 5 .20. Get List of Currencies Copyright © 2025 Skywind Holdings Ltd.
   - 5.21. Get List of Languages
   - 5.22. Get Jackpots
   - 5 .23. Promotions
      - 5.23.1. Create a Promotion...........................................................................................................
      - 5.23.2. Add Promo Rewards for Player
      - 5.23.3. Add Players to the Promotion
      - 5. 23 .4. Get a Promotion by ID
      - 5.23.5. Get a List of Promotions...................................................................................................
      - 5.23.6. Get Number of Free Bets for a Player
      - 5.23.7. Delete Player from the Promotion
      - 5.23.8. Archive a Promotion
   - 5.24. Change Player Nickname
- 6. Round Finalization
      - 6.1. Offline Payment
      - 6.2. Round Statistics
      - 6.3. Force-Finish
- APPENDIX
   - Test Account Provision
- APPENDIX
   - Reports API
- APPENDIX
   - Jackpot Contribution Reports
      - 1. Get Jackpot Contribution Report
      - 2. Get Players’ Jackpot Contribution Report
      - 3. Get Jackpot Contribution Logs..........................................................................................
      - 4. Get Jackpot Wins Logs
- APPENDIX
   - Whitelist Solution
- APPENDIX
      - CMA messages
- APPENDIX
      - Getting Critical Files from Games and Platform
      - 1. Get List of Critical Files and their Hashes from Games
      - 2. Get List of Critical Files and their Hashes from the Platform
      - 3. Get List of Critical Files and Their Hashes from Games for the Entity
      - 4. Get List of Critical Files and Their Hashes from the Platform for the Entity.....................
- APPENDIX
   - Retry Policy
- APPENDIX
   - Offline payments (Retransmissions)
- Document Version History


Copyright © 2025 Skywind Holdings Ltd.

## 1. Introduction

The main objective of the Seamless Integration Guide is to provide merchants with

information required for successful integration of Systems between the merchant and

the provider.

#### 1.1. Basic Definitions

- **API** – the interface used to provide communication between merchant and
    provider’s Systems.
- **Customer** – the merchant’s client.
- **ID** – identification number.
- **Merchant** – a gaming company/operator, which operates its own gaming site
    and has an agreement with Skywind Holdings Ltd.
- **Provider** – Skywind Holdings Ltd.
- **Request** – a request sent from the provider’s side.
- **Response** – a response received from the merchant’s side. An object in the
    response is an unordered set of name/value pairs.
- **Seamless Integration** – if a merchant uses Seamless Integration, the user
    account is managed from the merchant’s side.

#### 1.2. Description

It is the provider’s responsibility to develop the API for establishing communication

between the merchant and the provider’s platforms. The described API must support

the following basic methods:

- **Validate Ticket** – the method which enables sending the received ticket to the
    merchant’s platform for validation. If the ticket is valid, the merchant’s server
    will reply with the customer’s specific account data. If not, it will send an error.
    If the customer is new, the validate_ticket function will register the customer
    with Skywind Holdings Ltd.
- **Get Balance Operations** – a set of methods which enable to request the
customer’s current balance from the merchant’s side.
- **Debit Operations** – a set of methods which enable sending requests to debit
from the customer.
- **Credit Operations** – a set of methods which enable sending a request to credit
the customer’s balance with his win and money that he didn’t spend in the
game.
- **Rollback Bet Operations** – a set of methods which enable to send a request
to rollback the customer’s payment.
- **Free Bets** – the method enables to get the free bet count and coins value per
line (it is required only for Free Bets managed from the Operator`s side).


Copyright © 2025 Skywind Holdings Ltd.

- **Player API** : ${api.playerapi.com} represents a set of methods which provides
    by the merchant and realizes on its platform.
- **Operator API:** ${api.operatorapi.com} represents a set of methods which
    enable getting the information on the operator’s games.
- **Lobby API:** ${api.lobbyapi.com} represents a set of methods which are
    provided by the merchant and realized on its platform.
- **Report API:** ${api.reportapi.com} represents a set of methods which enable
    getting reports about currencies, players, and game history.

**Figure 1: Ticket usage in Seamless Integration**


Copyright © 2025 Skywind Holdings Ltd.

**Figure 2: Debit/Credit flow in Seamless Integration**

Once the API with the methods described above has been established on the

merchant’s side, the merchant should give the provider the links (see examples):

- https://api.example.com/api/validate_ticket
- https://api.example.com/api/get_balance
- https://api.example.com/api/debit
- https://api.example.com/api/credit
- https://api.example.com/api/rollback

To describe the methods, the terms request and response will be used. For each

request, appropriate response data including error codes will be returned.

The preferrable request and response format of API is JSON, however, for the legacy

systems we can enable the support of raw (form encoded) format.


Copyright © 2025 Skywind Holdings Ltd.

## 2. API Methods

### 2.1. Validate Ticket

**Request:** POST api.playerapi.com/api/validate_ticket HTTP/1.

This method sends a received ticket from the Provider’s System to the merchant’s side

for validation. If the ticket is valid, the merchant’s server will reply with the customer’s

specific account data. If not, it will send an error.

To get the validation, the following parameters should be entered according to the

scheme:

**Parameters**

Name Located in Required Type Notes

ticket body yes (^) string (64) Merchant’s ticket, used for
authentication and getting session ID.
merch_id body yes string (32) This parameter refers to the merchant’s
ID (set up on the Integration phase).
merch_pwd body yes (^) string (48) This parameter refers to the merchant’s
password (set up on the Integration
phase).
ip body no string (16) This parameter shows the player’s IP
address.
**Example of JSON Request**
Example
{
"ticket": "aXc2!f",
"merch_id": "gcxMerch",
"merch_pwd": "merchPassword"
}
The System responds to such requests according to the following schema:
**Response**
Code Schema Example
200 {
error_code: number,
cust_session_id: string,
cust_id: string,
currency_code: string,
test_cust: string ("true"|
"false") or
boolean(true|false),
country: string, (required)

##### {

```
"error_code": 0,
"cust_session_id": "custSessId",
"cust_id": "1234",
"currency_code": "GBP",
"test_cust": false,
"country": "GB",
"game_group": "Double Bets Group",
"rci": 60,
```

Copyright © 2025 Skywind Holdings Ltd.

**Notes:**

- The optional **game_group** parameter is used to override game limits. Game
    groups are managed by Skywind. **Please contact Customer Support to set**
    **up game groups.**
- The **cust_id** refers to the customer’s ID and includes numbers, letters, special
    characters, except a colon.
- If you use terminals and use the same endpoint for the terminal user login as
    for the **validate_ticket** method, it’s mandatory to add the **language**
    parameter to the **validate_ticket** response.
- **rci** and **rce** are optional response fields that shall be provided when the reality
    check (if it is needed by a regulator) is agreed to be tracked on the Skywind
    side. If it is true, **rce** (reality check elapsed time) and **rci** (reality check interval)
    params, when present in response, help Skywind to set a timer prior to the next
    reality check player pop-up (RC). **rci** and **rce** are measured in minutes and are
    replaced by the default values if they are absent in the response.
    **rce** param, when present, stands for how many minutes elapsed since the
    operator has shown the RC to a player the last time. **Example:** if the operator
    sends us **rci=15** , **rce=5** , it means that the operator wants us to show the RC
    every 15 minutes and that the operator has shown the RC to a player 5 minutes
    ago, so we will present it to a player in 10 minutes.
- By default, one **cust_session_id** should be used for several different games
    (you should not have in place a validation that **cust_session_id** is assigned
    to a specific game code). If your system cannot support it and you have unique
    tokens for each played game, you should implement Refresh Session Token
    method and request Skywind L2 Technical Integration team to enable this
    option for your entity.
- The **country** field is the required one. It determines the player location from
    the operator data. If the field is empty the player’s IP address from **5.9. Get**
    **Player’s Game URL** or **5.10 Get Fun Mode (Anonymous) Game URL** is
    used instead to detect the player’s location. The format is country code via ISO
    3166 - 2 standard.
- The **disable_offers** is an optional field. If true, Engagement hub features
    (MWJP, tournaments etc.) will be disabled for a player.

**Response**

Code Schema Example

```
game_group: string,
rci: number,
rce: number,
disable_offers: bool,
(optional)
nickname: string (optional)
}
```
```
"rce": 11 ,
"disable_offers": false,
"nickname": "Chronal"
}
```

Copyright © 2025 Skywind Holdings Ltd.

- Skywind’s response may change in the future to include more optional fields.
    There is no need to validate these additional fields.

For Error Response details see Section 4.

### 2.2. Refresh Session Token

**Request:** POST api.playerapi.com/api/refresh_session HTTP/1.

This method is used for merchant systems that do not allow a player to play different

games with the same session token. Skywind will call this method to obtain a new

token ( **new_cust_session_id** ) when a player opens a new game on the provider’s

website after having played another game.

The 'refresh session token' method is needed for Skywind live games as it allows to

enable the 'Back to Lobby' button, also it is required for a proper work of Skywind

Engagement Tools. The 'refresh session token' functionality is mandatory to provide

the full set of features for live games and Skywind Engagement Tools.

Please contact Skywind L2 Technical Integration team to enable this option for your

entity.

**Request Parameters**

```
Name Located
in
```
Required Type Notes

merch_id body yes (^) string (32) The merchant’s ID (set up on the
Integration phase).
merch_pwd body yes string (48) The merchant’s password (set up on the
Integration phase).
old_game_code body yes string ( 32 ) The code of the game the player opened
prior to opening the new game.
new_game_code body yes string ( 32 ) The code of the new game that the
player opens.
cust_id body yes string ( 32 ) The id of the customer (player).
cust_session_id body yes string ( 64 ) The id of the customer’s (player’s)
session preceding the launch of a new
game.
**Example of JSON Request**
Example
{
"merch_id": "EB",
"merch_pwd": "SoMEpassw",
"old_game_code": "sw_mrmnky",
"new_game_code": "sw_dc",
"cust_id": " 1234 ",
"cust_session_id": "SoMeSeSsionId" //
old customer session id


Copyright © 2025 Skywind Holdings Ltd.

}

The System responds to such requests according to the following schema:

**Response**

Code Schema Example

```
200 {
error_code: 0 ,
new_cust_session_id: string
( 64 )
}
```
##### {

```
"error_code": 0 ,
"new_cust_session_id": "NewSessionId"
}
```
For Error Response details see Section 4.

### 2.3. Get Balance Operations

```
▪ Debit – see method 2. 4. 1.
Event_type: bet
```
```
▪ Credit – see method 2. 4. 2.
Event_type: win
```
#### 2.3.1. Get Balance

**Request:** POST api.playerapi.com/api/get_balance

This method enables the customer’s current balance to be retrieved from the

Merchant’s System.

To get information about the customer’s balance, the following parameters can be

specified in the request to the System:

**Parameters**

Name Required Type Notes

merch_id yes (^) string (32) This parameter refers to the merchant’s ID
(set up on the Integration phase).
merch_pwd yes string (48) This parameter refers to the merchant’s
password (set up on the Integration phase).
cust_id yes (^) string (32) This parameter refers to the customer’s ID (it
can be numbers, letters, and special
characters, except a colon and backslash).
cust_session_id yes (^) string (64) This parameter refers to the customer’s
session ID.
game_code no string (32) This parameter refers to a code assigned to a
game. If a game has more than 1 math
version, it has a unique game code per each
math version.


Copyright © 2025 Skywind Holdings Ltd.

Name Required Type Notes

Platform no (^) string:
web | mobile
If specified, the request will return balance
information for the specified platform.
**Example of JSON Request**
Example
{
"merch_id": "EB",
"cust_id": " 1234 ",
"merch_pwd": "SoMEpassw",
"cust_session_id": "SoMeSeSsionId",
"game_code": "slot_game_1"
}
The System responds to such requests according to the following schema:
**Response**
Code Schema Example
200 {^
error_code: number,
balance: number,
currency_code: string (3),
free_bet_count: number (optional),
messages: Array (optional)
}

##### {

```
"error_code": 0,
"balance": 2055.15,
"currency_code": "GBP"
}
```
For Error Response details see Section 4.

**Notes:**

- The **free_bet_count** field is only used in the ‘Get Balance with FB’ request.
- The balance amount should be rounded and formatted according to ISO 4217
    for the players’ currency.
- The **messages** field is used for CMA messages, for additional information please
    read the Appendix 5 CMA messages section.

### 2.4. Transactions

**Note (for all transaction methods):** As we expect idempotency for all our

transactions, meaning multiple identical transaction requests must not multiply the

result, we require that in the case of receiving a duplicate transaction from us, you will

respond with error_code=1 (as stated in Error Responses section) and additionally

put balance into your response as if you were sending a response for the original

request.


Copyright © 2025 Skywind Holdings Ltd.

#### 2.4.1. Debit Customer

**Request:** POST api.playerapi.com/api/debit HTTP/1.

This method sends a request to the merchant’s side when the customer places a bet

on the provider’s site.

To send a request, the following parameters can be specified in the request to the

System:

**Parameters**

Name Required Type Notes

merch_id yes (^) string (32) This parameter refers to the merchant’s ID (set
up on the Integration phase).
merch_pwd yes string (48) This parameter refers to the merchant’s
password (set up on the Integration phase).
cust_id yes (^) string (32) This parameter refers to the customer’s ID (it can
be numbers, letters and special characters,
except a colon and backslash).
cust_session_id yes (^) string (64) This parameter refers to the customer’s session
ID.
round_id yes string (32) A string encoded representation of game_id (can
come as numeric game_id value if the string
encoded representation is not available).
**The value of this parameter is case
sensitive.**
Amount yes BigDecimal/
Integer
This parameter shows the amount that is used in
the debit operation. It can have an integer or a
decimal value.

- When the player wins a bonus (GRC win etc.)
in case a debit request is sent (depends on
integration) the bet will be 0.

```
currency_code yes string (3) This parameter shows the kind of currency that
is used in the debit operation.
```
game_code yes (^) string (32) This parameter refers to a code assigned to a
game. If a game has more than 1 math version,
it has a unique game code per each math
version.
trx_id yes (^) string (32) This is the original ID of the spin. The trx_id of
the debit request is the same as in the
corresponding credit request as we treat these
both transactions as a single spin. To distinguish
between credit and debit operations for the
same spin, please refer to the event_type field.
game_id yes BigInteger A unique numeric ID assigned to a game round


Copyright © 2025 Skywind Holdings Ltd.

Name Required Type Notes

```
event_type
yes string: “bet” |
“free-bet”
```
- Bet is used for regular spins and event_type:
“free-bet” is used for free bet spins.
- The debit amount shouldn’t be removed from
the real balance for event_type: “free-bet”.

event_id yes (^) BigInteger This parameter refers to a unique ID of an event.
timestamp yes (^) BigInteger The exact date and time of the debit transaction.
Used format is Unix time in seconds, time zone –
UTC.
game_type yes string: “normal” |
“freegame” |
“bonusgame”
This parameter shows the type of game in the
debit operation.

- “normal” – the player is playing a
    normal spin
- “freegame” – the playing is playing in-
    game free games/spins and the round
    isn’t over yet
- “bonusgame” the player is playing a
    bonus game

platform yes (^) string: “web” |
“mobile”
This parameter shows the platform where the
debit operation is used.
promo_id no string (32) Optional promotion id to help distinguish
different promotions.
promo_pid no string (32) This is the encoded promo_id value. We send it
if the promo_id is numeric, that is when sending
the /debit request before crediting the player
with Free Bets wins.
promo_external_
id
no string ID of external promotion, if provided by the
operator when creating the promotion.
jp_contribution no Decimal Optional request field that may be added upon
Operator request. Represents total amount
contributed to jackpots with this debit operation.
operation_ts yes (^) BigInteger Unix time (milliseconds from January 1, 1970
00:00:00 UTC) – the original time when the
payment was created (it does not change on
retries).
distribution_type no string: “network” |
“bespoke”
Optional request field that can be added at the
Operator’s request. This parameter distinguishes
a promotion as “network” if it was launched for
more than one brand, or as “bespoke” otherwise.


Copyright © 2025 Skywind Holdings Ltd.

**Example of JSON Request**

```
Example
{
"merch_id": "EB",
"merch_pwd": "SoMEpassw",
"cust_id": " 1234 ",
"cust_session_id": "SoMeSeSsionId",
"amount": 10 ,
"currency_code": "USD",
"game_code": "xc_craps",
"trx_id": "1345",
"game_id": 23,
"event_type": "bet",
"event_id": 3 ,
"timestamp": 248110400 ,
"game_type": "normal",
"platform": "web",
"jp_contribution": "0.00343",
"operation_ts": 1644484234950 ,
"distribution_type": "network"
"promo_id": "41243",
"promo_pid": "7RA8AdqG",
"promo_external_id": "12345",
}
```
The System responds to such requests according to the following schema:

**Response**

Code Schema Example

```
200 {^
error_code: number,
error_msg: string,
balance: number,
trx_id: string,
free_bet_count: number(optional),
messages: Array (optional)
}
```
##### {

```
"error_code": 0,
"balance": 2055.15,
"trx_id": " 1345 "
}
```
For Error Response details see Section 4.

**Note:**

- Balance amount should be rounded and formatted according to ISO 4217 for the

players’ currency


Copyright © 2025 Skywind Holdings Ltd.

- The **messages** field is used for CMA messages, for additional information please

read the Appendix 5 CMA messages section

- **trx_id** : please return the same value that was sent in the request.
- In the below mentioned cases we will automatically send the rollback request

(relevant only for new integrations):

- 500 response status code
- error code = - 1 (Internal Merchant error)
- unknown error code
- timeout
- As we expect idempotency for all our transactions, meaning multiple identical

```
transaction requests must not multiply the result, we require that in the case of
receiving a duplicate transaction from us, you will respond with error_code=1 (as
stated in Error Responses section) and additionally put balance into your response
as if you were sending a response for the original request.
```
#### 2.4.2. Credit Customer

**Request:** POST api.playerapi.com/api/credit HTTP/1.

This method allows the customer’s winnings and any money that they didn’t spend in

the game to be credited back to his balance.

To send a request, the following parameters can be specified in the request to the

System:

**Parameters**

Name Required Type Notes

merch_id yes (^) string (32) This parameter refers to the merchant’s ID (set
up on the Integration phase).
merch_pwd yes (^) string (48) This parameter refers to the merchant’s
password (set up on the Integration phase).
cust_id yes string (32) This parameter refers to the customer’s ID (it can
be numbers, letters and special characters,
except a colon and backslash).
cust_session_id yes (^) string (64) This parameter refers to the customer’s session
ID.
round_id yes (^) string (32) A string encoded representation of game_id (can
come as numeric game_id value if the string
encoded representation is not available).
**The value of this parameter is case
sensitive.**
amount yes (^) BigDecimal/
Integer
This parameter shows the amount that is used in
the credit operation. It can have an integer or a
decimal value.


Copyright © 2025 Skywind Holdings Ltd.

Name Required Type Notes

currency_code yes (^) string (3) This parameter shows the kind of currency that
is used in the credit operation.
game_code yes (^) string (32) This parameter refers to a code assigned to a
game. If a game has more than 1 math version,
it has a unique game code per each math
version.
trx_id yes string (32) This is the original ID of the spin. The trx_id
of the credit request is the same as in the
corresponding debit request as we treat these
both transactions as a single spin. To
distinguish between credit and debit operations
for the same spin, please refer to the
event_type field.
game_id yes (^) BigInteger A unique numeric ID assigned to a game round.
event_type yes (^) string: “win” |
“free-bet-win”

- Win is used for regular wins and event_type.
- “free-bet-win” is used for free bet wins.

jp_win no (^) boolean: “true” |
absent otherwise
If present and true means that this payout is a
jackpot payout.
event_id yes (^) BigInteger This parameter refers to a unique ID of an event.
timestamp yes (^) BigInteger The exact date and time of the credit transaction.
Used format is Unix time in seconds, time zone –
UTC.
game_type yes string: “normal” |
“freegame” |
“bonusgame”
This parameter shows the type of game in the
credit operation.

- “normal” – the player is playing a
    normal spin;
- “freegame” – the playing is playing in-
    game free games/spins and the round
    isn’t over yet;
- “bonusgame” the player is playing a
    bonus game.

game_status yes (^) string: “settled” |
“freegame” |
“bonusgame”
This parameter shows the game’s status that is
used in the credit operation.

- “settled” – the current credit operation is
    the last credit operation in this round,
    after this operation the round is closed.
- “freegame” – the player is playing in-
    game free spins and the round is not
    over yet;
- “bonusgame” – the player is playing a
    bonus game and the round isn’t over yet.

Platform yes (^) string: “web” |
“mobile”
This parameter shows the platform where the
credit operation is used.
operation_ts yes BigInteger Unix time (milliseconds from January 1, 1970
00:00:00 UTC) – the original time when the


Copyright © 2025 Skywind Holdings Ltd.

Name Required Type Notes

```
payment was created (it does not change on
retries).
```
jp_ids no (^) string [] This is an array in jackpot ids that is triggered on
the current spin, if the jp_win has been
responsed with ‘true’ result.
promo_id no string (32) Optional promotion id to help distinguish
different promotions.
promo_pid no string (32) This is the encoded promo_id value. We send it
if the promo_id is numeric, that is when crediting
the player with Free Bets wins.
promo_external_i
d
no string ID of external promotion, if provided by the
operator when creating the promotion.
distribution_type no (^) string: “network” |
“bespoke”
Optional request field that may be added upon
Operator request. This parameter distinguishes a
promotion as “network” if it was launched for
more than one brand, or as “bespoke” otherwise.
sm_result no string (4000) Round details formatted according to the
Portuguese regulation requirements.
**Example of JSON Request**
Example
{
"merch_id": "EB",
"merch_pwd": "SoMEpassw",
"cust_id": " 1234 ",
"cust_session_id": "SoMeSeSsionId",
"amount": 10 ,
"currency_code": "USD",
"game_code": "xc_craps",
"trx_id": " 1345 ",
"game_id": 23 ,
"event_type": "win",
"event_id": 3 ,
"timestamp": 248110400 ,
"game_type": "normal",
"game_status": "freegame",
"platform": "web",
"operation_ts": 1644484234950 ,
"distribution_type": "network",


Copyright © 2025 Skywind Holdings Ltd.

```
"sm_result": "0:2;3;4;2;3#2;5;5;2;3#0;7;6;4;3#"
```
}

The System responds to such requests according to the following schema:

**Response**

Code Schema Example

```
200 {^
error_code: number,
error_msg: string,
balance: number,
trx_id: string,
free_bet_count: number (optional),
messages: Array (optional)
}
```
##### {

```
"error_code": 0,
"balance": 2055.15,
"trx_id": " 1345 "
}
```
For Error Response details see Section 4.

**Notes:**

- The balance amount should be rounded and formatted according to ISO 4217 for

the players’ currency.

- The **messages** field is used for CMA messages, for additional information please

read the Appendix 5 CMA messages section.

- **trx_id** : please return the same value that was sent in the request.
- If the credit request has been unsuccessful, another credit request attempt is made

according to the retry policy.

- As we expect idempotency for all our transactions, meaning multiple identical

```
transaction requests must not multiply the result, we require that in the case of
receiving a duplicate transaction from us, you will respond with error_code=1 (as
stated in Error Responses section) and additionally put balance into your response
as if you were sending a response for the original request.
```
#### 2.4.3. Rollback (Refund) Customer’s Bet Transaction

**Request:** POST api.playerapi.com/api/rollback HTTP/1.

This method sends a request from the provider’s side to the merchant’s side stating

that the player needs to rollback their payment (bet) from the game.

Rollback (bet refund) operation can be applied only to debit (bet) operation. In other

words, rollback is triggered only by a failed debit (bet) operation.

The Rollback operation is initiated in one of the following cases:

- - 1 error code received from operator on /debit
- unknown error code (any code not listed in Error Responses) from operator on
    /debit


Copyright © 2025 Skywind Holdings Ltd.

- request timeout on /debit

**Note:** this method rollbacks only the last bet transaction in a round. This means that

if there was more than one bet transaction in a round, only the last failed bet must be

refunded.

To rollback a payment (bet), the following parameters should be entered according to

the schema:

**Parameters**

Name Required Type Notes

merch_id Yes string (32)

```
This parameter refers to the
merchant’s ID (set up on the
Integration phase).
```
merch_pwd Yes string (48)

```
This parameter refers to the
merchant’s password (set up on the
Integration phase).
```
cust_id Yes string (32)

```
This parameter refers to the
customer’s ID (it can be numbers,
letters and special characters, except a
colon and backslash).
```
```
cust_session_id Yes string (64)
This parameter refers to the
customer’s session ID.
```
round_id Yes (^) string (32) A string encoded representation of
game_id (can come as numeric
game_id value if the string encoded
representation is not available).
**The value of this parameter is
case sensitive.**
currency_code Yes string (3)
This parameter shows the kind of
currency that is used in the credit
operation.
game_code No string (32)
This parameter refers to a code
assigned to a game. If a game has
more than 1 math version, it has a
unique game code per each math
version.
trx_id Yes string (32) This^ is^ the^ original^ trx^ ID^ of^ the^ bet.^
game_id Yes BigInteger
A unique numeric ID assigned to a
game round.
event_type:
“rollback”
Yes string=’rollback’
“rollback” literal is set for transfer in
request.
**game_type** (^) yes
string: “settled” |
“freegame” |
“bonusgame”
This parameter shows the type of
game in the credit operation.
“normal” – the player is playing a
normal spin;


Copyright © 2025 Skywind Holdings Ltd.

Name Required Type Notes

```
“freegame” – the playing is playing
in-game free games/spins and the
round isn’t over yet;
```
```
“bonusgame” the player is playing
a bonus game.
```
```
game_status yes
```
```
string: “settled” |
“freegame” |
“bonusgame”
```
```
This parameter shows the game’s
status that is used in the credit
operation.
```
```
“settled” – the current credit
operation is the last credit
operation in this round, after this
operation the round is closed.
```
```
“freegame” – the player is playing
in-game free spins and the round is
not over yet;
```
```
“bonusgame” – the player is
playing a bonus game and the
round isn’t over yet.
```
```
event_id Yes BigInteger
This parameter refers to a unique ID
of an event.
```
Timestamp Yes BigInteger

```
The exact date and time of the credit
transaction. Used format is Unix time
in seconds, time zone – UTC.
```
operation_ts yes (^) BigInteger Unix time (milliseconds from January
1, 1970 00:00:00 UTC) – the original
time when the debit payment was
created (it does not change on retries).
**Example of JSON Request**
Example
{
"merch_id": "EB",
"merch_pwd": "SoMEpassw",
"cust_id": " 1234 ",
"cust_session_id": "SoMeSeSsionId",
"currency_code": "USD",
"game_code": "xc_craps",
"trx_id": " 1345 ",


Copyright © 2025 Skywind Holdings Ltd.

```
"game_id": 23 ,
```
```
"event_type": "rollback",
```
```
“game_type”: “normal”,
```
```
“game_status”: “settled”,
```
```
"event_id": 3 ,
```
```
"timestamp": 248110400 ,
```
```
"operation_ts": 1644484234950
```
}

The System responds to such requests according to the following schema:

**Response**

Code Schema Example

```
200 {^
error_code: number,
balance: number,
trx_id: string,
free_bet_count: number (optional)
}
```
##### {

```
"error_code": 0,
"balance": 2055.15,
"trx_id": " 1345 "
}
```
For Error Response details see Section 4.

**Notes:**

- The balance amount should be rounded and formatted according to ISO 4217
    for the players’ currency.
- **trx_id** : please return the same value that was sent in the request
- The failed debit request doesn’t trigger ‘retry’ mechanism, whereas the failed
    rollback request does.
- As we expect idempotency for all our transactions, meaning multiple identical
    transaction requests must not multiply the result, we require that in the case of
    receiving a duplicate transaction from us, you will respond with **error_code=1**
    (as stated in Error Responses section) and additionally put balance into your
    response as if you were sending a response for the original request.
- In case the offline payment request is sent to the operator wallet, the
    operator’s system should validate the player’s round and, if applicable,
    validate that the "cust_session_id" sent by Skywind, although it might be
    expired, is the same token which was used in the corresponding/latter credit
    request.

**Error codes that operator must return for rollback operation**

- If the operator receives /rollback request and the transaction to cancel is not
    found, then **error_code=- 7 (Transaction not found)** must be returned.


Copyright © 2025 Skywind Holdings Ltd.

- If the operator receives /rollback request and transaction to cancel has already
    been cancelled, then **error_code=1 (Duplicate transaction)** must be
    returned along with balance data.
- The rollback is considered successful when receiving the error codes 1 and - 7,
    so Skywind will not do any retries.


Copyright © 2025 Skywind Holdings Ltd.

### 2.5. Player Logout

**Request:** POST api.playerapi.com/api/logout_player HTTP/1.1

This method lets you get a notification when a player left the game meaning they

closed the window or were absent for some time. This method is not enabled by

default, but you can request it from our Support team.

When requesting to enable the ‘Player logout’ method, please specify the following:

- Whether you want to be notified each time a player left the game or only if a
    player left in the middle of an unfinished round.
- How long a player should be inactive (the game window is open, but the player
    does not play) before we send the logout notification.

The following parameters should be entered according to the schema:

**Parameters**

```
Name Required Type Description
```
```
merch_id
Yes^
```
```
string (32) This parameter refers to the merchant’s ID
(set up on the Integration phase).
```
```
merch_pwd
yes
```
```
string (48) This^ parameter^ refers^ to^ the^ merchant’s^
password (set up on the Integration phase).
```
```
cust_id no string (32)
Optional parameter, which requires a token
to be generated for a particular customer.
```
cust_session_id yes (^) string (64) (^) This parameter refers to the customer’s
session ID.
round_id yes (^) string (32) A string encoded representation of game_id
(can come as numeric game_id value if the
string encoded representation is not
available).
**The value of this parameter is case
sensitive.**
game_code no (^) string (32) This parameter refers to a code assigned to
a game. If a game has more than 1 math
version, it has a unique game code per each
math version.
game_id yes (^) BigInteger (^) A unique numeric ID assigned to a game
round.
logout_id yes (^) string (32) This parameter refers to a unique ID of a
logout event.
timestamp yes BigInteger Timestamp of logout event, in seconds.


Copyright © 2025 Skywind Holdings Ltd.

**Example of JSON Request**

```
Example
{
"merch_id": "EB",
"merch_pwd": "SoMEpassw",
"cust_id": " 1234 ",
"cust_session_id": "SoMeSeSsionId",
"game_code": "sw_sod",
"logout_id": "logoutId ",
"game_id": 4562 ,
"round_id ": " sWeq1eT",
"round_state": "finished",
"timestamp": 154353432289
}
```
The System responds to such requests according to the following schema:

**Response**

```
Code Schema Example
```
```
200 {^
error_code: number
}
```
##### {

```
"error_code": 0
}
```

Copyright © 2025 Skywind Holdings Ltd.

### 2.6. Get Ticket

**Request:** POST api.playerapi.com/api/get_ticket HTTP/1.1

This method generates a valid ticket for a test user on the merchant’s side.

To successfully pass the integration tests phase of your integration with Skywind, it is

mandatory to fulfil one of the following requirements:

1) to implement the 'get_ticket' method as it is used for getting a test ticket from your

API for running the integration test,

2) to provide our Support Team with means for obtaining a test ticket for your API

```
for running the integration test.
Note: the test ticket you provide must be able to pass the 'validate_ticket' check
and have the'test_cust' field value of the'validate_ticket' response set as true.
```
To get the validation, a list of the following parameters should be entered according

to the schema:

**Parameters**

```
Name Required Type Description
```
```
merch_id
yes
```
```
string (32) This^ parameter^ refers^ to^ the^ merchant’s^ ID^ (set^
up on the Integration phase).
```
```
merch_pwd
yes
```
```
string (48) This^ parameter^ refers^ to^ the^ merchant’s^
password (set up on the Integration phase).
```
```
cust_id no string (32)
Optional parameter, which requires a token to be
generated for a particular customer.
```
```
single_session no boolean
Optional boolean flag, which requires any
previous sessions for this customer to be killed.
```
```
currency_code no string (3)
Optional string parameter, which requires a
token to have a particular currency_code.
```
```
Note:
If the “cust_id” field is not specified or the player with such “cust_id” doesn’t exist, a new player
should be created. If the player exists, the operator should validate a received “currency_code” which
is matched with player’s currency and this player has the “test_cust: true” parameter.
```
**Example of JSON Request**

```
Example
{
"merch_id": "EB",
"merch_pwd": "SoMEpassw",
"cust_id": " 1234 ",
```

Copyright © 2025 Skywind Holdings Ltd.

```
"single_session": "true",
"currency_code": "USD"
}
```
The System responds to such requests according to the following schema:

**Response**

```
Code Schema Example
```
```
200 {^
ticket: string
}
```
##### {

```
"ticket": "UQ2hd9awiohdbahiwddawd"
}
```

Copyright © 2025 Skywind Holdings Ltd.

## 3. Marketing Tools

Marketing tools help to increase the player retention and encourage them to spend

more time in the games. These tools can be installed and managed directly from a

merchant’s platform or from the Falcon Integration System (FIS) side upon the

merchant’s request.

### 3.1. Free Bets...........................................................................................................................

Free Bets is a type of promotion where a player is awarded a number of free bets

that can be used in real game mode.

Please distinguish between Free Bets and Free Games:

1) **Free Games** is an in-game feature. The way Free Games are triggered is unique

to each game and is described in the game rules or the game paytable. Free Games

are a part of the game math, you can’t enable/disable them for the game. In some

games, Free Games can also be referred to as Free Spins.

2) **Free Bets** is a cross-game engagement tool that you can switch on for the games

that support this feature. It’s up to the player at which point of the game to use the

awarded Free Bets. The Free Bets can be used across all games they are enabled for.

**The Operator can choose on which side Free Bets are created and managed**

**- FIS side or the Operator’s side.**

If Free Bets are managed by FIS:

- Free Bets are created, configured, and managed as a part of the Promotion
    mechanism. Please see the required methods under Promotions.
- Free Bets can be created in the Unified Back Office system: Engagement >
    Promotions.
- Operator does not have to implement get_free_bet endpoint.

The operator can set per player (or for all players) how many FBs he has and in which

games FBs can be used. The win amount will be determined by the coin value which

was set by the operator (multiplied by the max lines/total bet amount). Every bet

decreases the player’s FB count by 1.

If Free Bets are managed by the Operator:

- Free Bets creation, configuration and management are the responsibility of an
    Operator, please see the required methods below in this section.
- Operator must implement get_free_bet endpoint
- Operator must return free_bet_count property as part of get_balance response


Copyright © 2025 Skywind Holdings Ltd.

Below is an approximate flow for Free Bets flow in FIS and Operator systems when

they are managed by the Operator.

**Figure 3 : Free Bets (managed by Operator) flow in Seamless Integration**

#### 3.1.1. Get Free Bets

**Request:** POST api.playerapi.com/api/get_free_bet

This method is optional and shall be implemented only if the Operator decides to

manage Free Bets completely on their side.

The method retrieves the free bet count and coins value per line. The total bet is

determined by the coin_multiplier * free_bet_coin. The free_bet_coin can be one from

stake all.

There are several options for the operators to manipulate free bets depending on their

needs:

1. Operators can award a player with FBs in a certain game by specifying the
    game_code parameter in the request. In this case the FBs won’t be available to
    the player in other games.
2. Operators can give different coin values per request.


Copyright © 2025 Skywind Holdings Ltd.

3. Operators have the player’s information (VIP level, previous FBs used, etc.) and
    can use it to define FBs.

To get the free bet coin per line, the following parameters should be specified in the

request to the System:

**Parameters**

Name Required Type Notes

```
cust_id yes string (32) This parameter refers to the customer’s ID
(it can be numbers, letters and special
characters, except a colon and backslash).
```
cust_session_id yes (^) string (64) Unique session ID.
merch_id yes (^) string (32) This parameter refers to the merchant’s ID
(set up on the Integration phase).
merch_pwd yes (^) string (48) This parameter refers to the merchant’s
password (set up on the Integration phase).
game_code yes (^) string (32) This refeers to a code assigned to a game.
If a game has more than 1 math version, it
has a unique game code per each math
version.
coin_multiplier yes (^) number This parameter shows the number of
multipliers.
stake_all yes (^) string This parameter shows an amount of
common stake.
**Example of JSON Request**
Example
{
"merch_id": "EB",
"cust_id": " 1234 ",
"merch_pwd": "SoMEpassw",
"cust_session_id": "SoMeSeSsionId",
"game_code": "sw_sod",
"coin_multiplier": " 50 ",
"stake_all": "0.1,0.2,0.5,1,2"
}
The System responds to such requests according to the following schema:
**Response**
Code Schema Example
200 {^
error_code: long,
free_bet_count: number (optional),
free_bet_coin: number (optional)
}

##### {

```
"error_code": 0,
"free_bet_count": 10,
"free_bet_coin": 0.1
}
```

Copyright © 2025 Skywind Holdings Ltd.

For Error Response details see Section 4.

#### 3.1.2. Debit Customer with Free Bets..........................................................................................

**Request:** POST api.playerapi.com/api/debit HTTP/1.1

This method sends a request to the merchant’s side when the customer places a bet

on the provider’s site.

**Parameters to specify** : same as in 2.2.0. Debit customer. **The only difference is**

**the value** of event_type param – it must be "free-bet".

**Note** : in our protocol the bet amount and free_bet_coin are sent for /debit

operation for "event_type" = "free-bet".

**Example of JSON Request**

```
Example
{
"merch_id": "EB",
"merch_pwd": "SoMEpassw",
"cust_id": " 1234 ",
"cust_session_id": "SoMeSeSsionId",
"amount": 10 ,
"currency_code": "USD",
"game_code": "xc_craps",
"trx_id": "1345",
"game_id": 23,
"event_type": "free-bet",
"free_bet_coin": 0.1,
"event_id": 3 ,
"timestamp": 248110400 ,
"game_type": "normal",
"platform": "web",
"operation_ts": 1644484234950
"promo_id": "41243",
"promo_pid": "7RA8AdqG",
"promo_external_id": "12345",
"game_status": "settled"
}
```

Copyright © 2025 Skywind Holdings Ltd.

The System responds to such requests according to the following schema:

**Response**

Code Schema Example

```
200 {^
error_code: long,
error_msg: string,
balance: number,
trx_id: string,
free_bet_count: number(optional,
applicable for free-bets managed by
Operator)
}
```
##### {

```
"error_code": 0,
"balance": 2055.15,
"trx_id": " 1345 ",
"free_bet_count": 10
}
```
**Notes:**

- **trx_id** : please return the same value that was sent in the request.

For Error Response details see Section 4.

#### 3.1.3. Credit Customer with Free Bets

**Request:** POST api.playerapi.com/api/credit HTTP/1.1

This method credits the customer’s balance with winnings and any money that he

didn’t spend in the game.

**Parameters to specify** : same as in 2.3.0. Credit customer. **The only difference**

**is** the value of event_type param – it must be ‘free-bet-win’.

**Example of JSON Request**

```
Example
{
"merch_id": "EB",
"merch_pwd": "SoMEpassw",
"cust_id": " 1234 ",
"cust_session_id": "SoMeSeSsionId",
"amount": 10 ,
"currency_code": "USD",
"game_code": "xc_craps",
"trx_id": "1345",
"game_id": 23,
"event_type": "free-bet-win",
"event_id": 3 ,
"timestamp": 248110400 ,
"game_type": "normal",
"platform": "web",
```

Copyright © 2025 Skywind Holdings Ltd.

```
"operation_ts": 1644484234950,
"promo_id": "41243",
"promo_pid": "7RA8AdqG",
"promo_external_id": "12345",
"game_status": "settled"
}
```
The System responds to such requests according to the following schema:

**Response**

Code Schema Example

```
200 {
error_code: long,
balance: number,
trx_id: string,
free_bet_count: number (optional)
}
```
##### {

```
"error_code": 0,
"balance": 2055.15,
"trx_id": " 1345 ",
"free_bet_count": 3
}
```
**Notes:**

- **trx_id** : please return the same value that was sent in the request.

For Error Response details see Section 4.

### 3.2. FunBonus Management

FunBonus is a specialized promotional tool that allows operators to award players with bonus funds that can only be used in dedicated game sessions. These bonuses provide an engaging way to increase player retention while maintaining clear separation from real money gameplay.

#### 3.2.1. FunBonus Overview

Fun bonuses assigned by the operator can only be used in special game sessions. In these sessions, the awarding of jackpots is allowed, powered exclusively by fun bonus. "Real Jackpot" and "fun jackpot" must have separate contributions. When launching a fun bonus session, the game version must be without jackpot.

**Key Features:**
- Fun bonuses cannot be used in combination with real money and real bonus in the initial stake
- Bets made using fun bonus do not allow cash winnings and do not contribute to tax calculations
- Winnings from Fun bonus bets will always be fun bonus
- Fun Bonuses have a conversion condition through "wagering rules"
- Fun Bonuses will be converted to real bonuses only when the wagering rule has been satisfied
- Full game mechanics support with game history and round history
- Currency exchange between funBonus currencies is supported
- Split payments and revert operations are supported

**Limitations:**
- Real money transactions are not allowed
- Jackpot functionality is disabled
- Mixed currency gameplay (real + funBonus) is not supported

#### 3.2.2. FunBonus Wagering Rules

Fun Bonuses include a conversion mechanism based on wagering requirements. Players must meet specific wagering conditions to convert Fun Bonus balance into Real bonus.

**Example:**
The operator awards 10€ fun bonus with wagering 35. This means that the player must bet 10€ × 35 = 350€ during fun bonus sessions to convert Fun Bonus balance into Real bonus.

All fun bonus promotions and wagering are managed by the operator.

#### 3.2.3. Game Launch Integration

**Game URL Parameters:**
When launching games with FunBonus currencies, the system will automatically handle the appropriate play mode configuration. No additional parameters are required beyond the standard currency specification.

**Game Launch Request:**
```http
GET /games/{gameCode}?currency={funBonusCurrency}
```

**Parameters:**

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `gameCode` | string | Game identifier | Yes |
| `currency` | string | FunBonus currency code | Yes |

**Response:**
The game URL will be configured for FunBonus gameplay:

```json
{
  "url": "https://game.example.com/game?token=...",
  "playMode": "fun_bonus"
}
```

#### 3.2.4. Transaction Response Messages

**Message Parameters:**
The system supports additional parameters in credit transaction responses to display conversion messages to players:

```json
{
  "messageTitle": "Fun bonus",
  "messageBody": "You won 100 real bonus from promo Example",
  "messageBodyHtml": "PHA+WW91IHdvbiAxMDAgcmVhbCBib251cyBmcm9tIHByb21vIEV4YW1wbGU8L3A+"
}
```

**Message Display Requirements:**
- A popup must be shown to the player using values of "messageTitle" and "messageBody" (or alternatively "messageBodyHtml")
- This message is triggered when the conversion threshold has been reached (internally by operator Platform)
- The player should not be allowed to close the message and return to play in the game screen
- The message box must not have any buttons inside

#### 3.2.5. Error Handling

**Common Error Responses:**

| HTTP Status | Error Message | Description | Solution |
|-------------|---------------|-------------|----------|
| 400 | Currency validation failed | Invalid FunBonus currency | Use valid FunBonus currency codes |
| 400 | Invalid currency formatting | Currency precision error | Check currency format and precision |
| 403 | Real money prevention | Attempt to mix real and fun bonus | Use FunBonus currencies only |
| 404 | Currency not found | Currency does not exist | Verify currency is configured |
| 400 | Validation error | Invalid parameters | Check request parameters |

**Best Practices:**
- Always validate currency codes before making requests
- Handle conversion messages appropriately in your game client
- Implement proper error handling for all FunBonus operations
- Ensure game UI clearly indicates FunBonus mode to players

### 3.3. Other Marketing Tools

Other marketing tools offered by Skywind include Tournaments, Must Win Jackpots

and Prize Drops.

- **Tournaments** – cross-game promotions where players earn points by making
    bets, improve their position on the leaderboard and earn prizes on reaching
    the highest ranks.
- **Must Win Jackpots** – cross-game jackpots that drop according to predefined
    conditions (time limit, amount limit, etc.)
- **Prize Drops** – a type of promotion when any real money bet which meets the
    minimum bet requirement can win a random prize for the player from the list
    of the available prizes.


Copyright © 2025 Skywind Holdings Ltd.

#### 3.3.1. Bonus Payment (Credit a Customer with a Bonus Win)

**Request:** POST api.playerapi.com/api/bonus HTTP/1.1

This method allows you to credit the customer’s balance with a bonus win when using

one of Skywind’s engagement tools such as Tournaments, Must Win Jackpots (Shared

Prize) or Prize Drops.

**Tournaments, Must Win Jackpot (Shared Prize) and Prize Drops Payments**

For Tournaments, Prize Drops and Must Win Jackpot (Shared Prize) this method will

credit the player’s balance whether the player is online or offline. Additionally, the

bonus payment will not be written on the players’ game history, as it is not a part of a

specific game play.

To send a request, the following parameters can be specified in the request to the

System:

**Parameters**

Name Required Type Notes

cust_id yes (^) string (32) Customer ID (can include numbers, letters and
special characters, except “:” and “\”).
merch_id yes string (32) Merchant ID, set up prior to the integration.
amount yes string Amount of money transferred in this credit
operation. This parameter has a string number
value.
currency_code yes (^) string (3) Currency used in this credit operation.
trx_id yes (^) string (32) Transaction ID generated in Skywind’s system
and sent to the operator for reference.
timestamp yes (^) BigInteger Date and time of the credit transaction.
promo_id yes string (32) Unique ID of the promotion.
If the operator specifies externalId when creating
a promo via API, then we will send externalId
value in the promo_id field instead of the promo
ID assigned to the promotion in Skywind system.
promo_type no (^) string:
"tournament"|
"shared_jp_prize"|
"prize_win"
Optional field which provides additional info on
the source of the bonus payout. This field may be
changed to send new bonus type payouts or
removed completely from the API.
Currently supported values:

- “tournament" = tournament payout
- "shared_jp_prize" = must win jackpot
    shared prize payout
- "prize_win" = Prize Drop feature payout

merch_pwd yes/no (^) string (48) Merchant’s password, set up in the integration
phase.


Copyright © 2025 Skywind Holdings Ltd.

hash no (^) string (32) Hash code for checking if the request is valid.
operation_ts no BigInteger Optional request field that can be added at the
Operator’s request. Unix time (milliseconds from
January 1, 1970 00:00:00 UTC) – the original
time when the payment was created (it does not
change on retries).
distribution_type no string: “network” |
“bespoke”
Optional request field that can be added at the
Operator’s request. This parameter distinguishes
a promotion as “network” if it was launched for
more than one brand, or as “bespoke” otherwise.
**Note:** The **merch_pwd** parameter is optional if the authentication option with the hash
parameter is selected.
The number of parameters can be changed in the future which will affect the hash
calculation. Please note that the hash calculation must support adding new parameters.
**Authentication**
There are 2 merchant authentication options:

1. by using the **merch_pwd** parameter
2. by using the **hash** parameter

Both methods require pre-configuration on Skywind side, please contact Skywind

support for assistance.

To calculate the hash code for the authentication with the hash parameter, take all

parameters from the request (except hash) and append them to the string by following

the steps below:

1. Sort all parameters by keys in alphabetical order.
2. Append them (if the value is not null or empty) in key1=value1&key2=value2.
3. Append secret ( **merch_pwd** ), e.g.: key1=value1&key2=value2SECRET.
4. Calculate the hash by using MD5.
5. Compare with the hash parameter. In the case of a failure, Casino Operator
    should send the error code - 1.

**Example of JSON request**

```
Example
```
```
{
"merch_id": "1234",
"promo_id": "1234234",
"promo_type": "tournament",
"amount": 100,
"currency_code": "USD",
"cust_id": "player001",
"timestamp": 1600780730294,
```

Copyright © 2025 Skywind Holdings Ltd.

```
"trx_id": "23234dsdfasdfasdr5213",
"hash": "hash",
"operation_ts": 1644484234950,
"distribution_type": "network"
}
```
The System responds to such requests according to the following schema:

**Response**

Code Schema Example

```
200 {^
error_code: number,
error_msg: string,
balance: number,
trx_id: string
}
```
##### {

```
"error_code": 0,
"balance": 2055.15,
"trx_id": " 1345 "
}
```
**Note:** Balance amount should be rounded and formatted according to ISO 4217 for

the players’ currency.

For Error Response please refer to 4. Error Responses.


Copyright © 2025 Skywind Holdings Ltd.

## 4. Error Responses

If any issues occur while performing one of the operations, the merchant’s system

should respond with the error codes from the table below. For all errors that are

intentionally returned to us we expect to receive HTTP Status = 200.

If the System has an error code that doesn’t equal 0 in the response, it should be

explained in error_msg.

**General Errors**

```
Seamless
Error Code
```
Text Description

0 No errors

```
1 Duplicate transaction As we expect idempotency for all our
transactions, we require that in the case
of receiving a duplicate transaction from
us (a duplicate debit/credit request with
the same trx_id), you will respond with
error_code=1 and the balance value as in
the response to the original request.
```
- 1 Merchant internal error An unexpected error was encountered
    while processing the request. It can be
    caused by a validation error, for example,
    if the merchant doesn’t exist. Operator
    can use this error when they don’t expect
    such behavior from the provider’s side.
- 2 Player not found Returned when the game is launched with
    the playerCode which doesn’t exist on the
    merchant’s side or if the playerCode has
    an invalid value, for example, is empty or
    has a number value type.
- 3 Game token expired Player session has expired or has invalid
    value.
- 301 Player is suspended Player account is inactive, and they can’t
launch games. This error is the extension
of the - 3 error.
- 302 Bet Limit Was Exceeded The bet limit for this player session has
been exceeded. This error is the
extension of - 3 error.
- 4 Insufficient balance Player does not have sufficient balance to
    perform an operation.
- 5 Insufficient free bets Balance Returned when the number of free bets
    for the player is 0.


Copyright © 2025 Skywind Holdings Ltd.

```
Seamless
Error Code
```
Text Description

- 6 Invalid free bet Returned when the request contains the
    invalid value of the free bet parameter,
    for example, when the Operator expects
    the free bet promotion is stored on their
    side, but the promotion was created on
    Provider’s side and the values of free bet
    parameters don’t match.
- 7 Transaction not found Can be returned in the case of a rollback
    operation when the transaction id in the
    request is invalid

**Note** :

- For the response with the HTTP status 200 we apply the mapping of errors
    depending on the error_code in the response. For the error response with the
    HTTP status = 200 we make retries of a request if:
       - The request is either of /debit, /credit and /rollback.
       - The outcome of the request is unclear: - 1 error code from the merchant,
          unknown error code from the merchant.
- For the response with the HTTP status which is greater than or equals 400 and
    is less than 500 (400 ≤ statusCode < 500) we do not do retries and initiate our
    internal error processing mechanism.
- For the response with the HTTP status >= 500, we can make retries of a failed
    request if:
       - The request is either of /debit, /credit and /rollback.
       - The outcome of the failed request is unclear: 5xx response status code
          or request timeout.

```
Please note that in case of failure, retries are made with an exponential pause
time until the Total Request Time limit is reached ( 10 seconds by default).
```
```
Once the Total Request Time limit is reached, we initiate our internal error
processing mechanism.
```
**Responsible Gaming Errors**

```
Seamless Error
Code
```
Text

- 1500 Player is on timeout
- 1501 Player is self-excluded
- 1502 Player has reached his deposit limit
- 1503 You have reached your session time limit
- 1504 You have reached your daily time limit


Copyright © 2025 Skywind Holdings Ltd.

```
Seamless Error
Code
```
Text

- 1505 Reality check error
- 1506 Player is permanently blocked
- 1507 The bet amount you selected exceeds your loss limit for today
- 1508 You’ve reached your loss limit for today
- 1509 The bet amount you selected exceeds your loss limit for this week
- 1510 You’ve reached your loss limit for this week
- 1511 The bet amount you selected exceeds your loss limit for this month
- 1512 You’ve reached your loss limit for this month
- 1513 You’ve reached your session loss limit
- 1514 Player has exceeded regulatory limits
- 1515 You’ve reached your session bet limit
- 1516 You’ve reached your bet limit for today
- 1517 You’ve reached your bet limit for this week
- 1518 You’ve reached your bet limit for this month
- 1519 You’ve reached your withdraw limit
- 1520 Mandatory limit missing
- 1521 You’ve reached your session game group loss limit
- 1522 You’ve reached your game group loss limit for today
- 1523 You’ve reached your game group loss limit for this week
- 1524 You’ve reached your game group loss limit for this month
- 1525 You have exceeded max bet for bonus funds
- 1526 Blocked from playing - daily time limit exceeded
- 1527 Blocked from playing - weekly time limit exceeded
- 1528 Blocked from playing - monthly time limit exceeded
- 1529 Your session has now exceeded <playerSessionTime>. We would like to
    notify you that you've already played for your reality check interval


Copyright © 2025 Skywind Holdings Ltd.

Custom Behaviour Errors

```
Seamless
Error
Code
```
```
Text
```
- 1601 Your bet was not accepted and your account has not been charged. If you
    are using a bonus please check your bet configuration or contact Customer
    Support for assistance.

**Notes** :

```
Error code – 1601 has the following behavior: it stops the reels on the default
positions and displays a localized message to the player
```

Copyright © 2025 Skywind Holdings Ltd.

## 5. Falcon API

The Falcon API represents a set of methods which allow information regarding the

operator’s games to be retrieved, including jackpot details.

### 5.1. Endpoints and Routing

The Falcon Integration API is completely RESTful and accepts GET, POST, PUT, PATCH

and DELETE requests, depending on the resource.

The base endpoint URL enables access to all recourses of the Integration System -

${api.integrationsystem.com}.


Copyright © 2025 Skywind Holdings Ltd.

POST https://${api.integrationsystem.com}/v1/example/ HTTP/1.1

Content-Type: application/json

{

"name": "example"

}

The Integration System API provides a selection of response codes, content-type

headers, and other options to help interpret the responses to the API requests.

**Request** :

GET https://api.integrationsystem.com/v1/example/ HTTP/1.1

**Response** :

HTTP/1.1 200 OK

Content-Type: application/json

{

"name": "example",

}

All API requests and responses are in the JSON format.

Sometimes the API call will generate an error. Every response to an API call that

generates an error will include an error code and error message to help understand

the reason for the error.

**Response with Error** :

{

code: 51

message: "Name is missing"

}

### 5.2. Parameters Format


Copyright © 2025 Skywind Holdings Ltd.

For the methods in Falcon API, the date/time parameters such as firstTs follow ISO

8601 format unless a different date/time type is specified. For example, '2016- 12 -

10T16:47:38.887Z'.

Pay attention, that Skywind system **always** returns data related to time and dates in

**UTC time zone.**

### 5.3. Multi Domain

The Skywind System has different services, which are located on different domains

and united in one Multi-Domain Solution. This Multi-Domain Solution gives an

unprecedented level of flexibility when using the System. Currently unavailable

domains can be switched to a new one on the fly and quickly restore lost traffic.

This solution also allows different routes between Operators and Skywind servers to

be chosen and minimizes communication delays.

During the integration process, to support the Multi-Domain Solution, the Operator has

to take into consideration that Skywind services’ domain names are subject to change.

Thus, the Skywind Integration Team recommends storing Skywind services’ domain

names as separate/external variables, which can be easily updated.

Regardless of the domain name, API methods and their signatures stay the same

(unless some changes are made to the method itself).

Initial domains for the System’s endpoints are provided during the integration process.

If the Operator faces any issues blocking domains during live operations, the Skywind

Support Team will be notified of the issue and provide the Operator with a new domain

name. It is also possible to request new domains by sending a request to the Skywind

Support Team.


Copyright © 2025 Skywind Holdings Ltd.

### 5.4. Authentication

To carry out API integration, the user gets a unique username, password and secret

key to log into the system and retrieve the token. This should be entered in every

request header to enable operation within the system.

JSON Web Token (JWT) is used as an authenticating mechanism. It is an open

standard (RFC 7519) that defines a compact and self-contained way of securely

transmitting information between parties as a JSON object. This information can be

verified and trusted because it is digitally signed.

JWTs can be signed using a secret (with the HMAC algorithm) or a public/private key

pair using RSA.

The following diagram shows authentication process:

The advantages of JWT:

- **Compact:** Because of their smaller size, JWTs can be sent via a URL, POST
    parameter or inside an HTTP header. The smaller size means transmission is
    faster.
- **Self-contained:** The payload contains all the required information about the
    user, avoiding the need to query the database more than once.


Copyright © 2025 Skywind Holdings Ltd.

After the user logs in, they are given the following permissions:

- Request a list of

o games

o countries

o languages

o currencies available for the operator

- Create a player
- Deposit to/withdraw from a player
- Get a URL of the player’s game
- Get game info

### 5 .5. User Login

**Request:** POST api.operatorapi.com/login HTTP/1.1

To receive a token and gain access to the System, the user must log in.

Initially, the operator is provided with a unique username, password, and a secret key

for every user. This should be sent in order to log in.

**Parameters to enter**

```
Name Located In Required Schema Note
```
info body yes (^) {
secretKey: string,
username: string,
password: string
}
This parameter refers to
the System user’s data.
**Example of JSON request**
Example
{
"secretKey": "UserKey",
"username": "UserName",
"password": " UserPassword123",
}
In response to the entered username, password and secret key, the System generates
a token string to identify the user’s commands:


Copyright © 2025 Skywind Holdings Ltd.

**Example HTTP response**

```
HTTP Code Schema Example
```
```
200 {^
username: string (32)
accessToken: varchar,
key: string (64)
}
```
```
User has been created:
```
```
{
"key": "0458d0f8- 5040 - 4f04- 8548 -
fc2570b12626",
"username": "USER",
"accessToken": "token"
}
```
The token should be entered in all request headers in the relevant format ‘X-ACCESS-

TOKEN: string’.

If any issues occur while entering the System, it responds with the following error

codes:

```
HTTP Code Error Code Description
```
```
400 998 Login Failed
```
##### 401

##### 230

```
Login Failed, please contact
Skywind Support to resolve the
issue
```
```
715
Two Factor Authentication is
not set for user
```
##### 719

```
An error occurred when
sending sms
```
```
720
An error occurred when
sending email
```
##### 409 727

```
Two factor auth code has been
sent recently. Repeat attempt a
little later
```

Copyright © 2025 Skywind Holdings Ltd.

### 5.6. Refresh Access Token

**Request:** POST api.operatorapi.com/login/refresh HTTP/1.1

The method enables the user to refresh the login information before the token expires.

The access token expiration time for the Back-office user is 15 minutes, for the API

user – 1 hour. The System sends the token to the Integration API and gets it back to

log the user in again.

The System responds to such requests according to the following schema:

**Response**

```
Code Schema Example
```
```
200 LoginInfo {^
key: string (64),
username: string (32),
accessToken: varchar
}
```
##### {

```
"key": "4e85fb5c-cc34-
472b-ac4a-69bbcf1b73c0",
"username": "USER1",
"accessToken": "token"
}
```
If any issues occur while refreshing the login information, the System responds with

the following error codes:

```
HTTP Code Error Code Description
```
##### 400 62

```
One of the parents is
suspended
```
##### 401

```
10 Access Token is missing^
```
```
204 Access token error^
```
```
205 Access Token is expired^
```
```
792 Access Session is expired^
```
```
404 51 Could not find entity
```

Copyright © 2025 Skywind Holdings Ltd.

### 5 .7. Get List of Games

**Request:** GET api.operatorapi.com/games/info/search HTTP/1.1

The user can get a list of the games with the base information available for its operator.

To do this, the following parameters should be entered according to the schema:

**Parameters**

Name Located In Required Type Notes

offset query no (^) integer Number of items to be
skipped from the beginning of
the selection (default – 0).
limit query no integer Number of items to return
(default – 20).
sortBy query no (^) string (32) Sorting fields. Allowed values
are ‘code’, ‘title’,
‘providerTitle’, ‘providerCode’,
‘categoryList’.
sortOrder query no string (4) Sorting order: ASC or DESC.
code query no (^) string (32) Game code to search for.
code__in query no (^) string (64) Game codes to search for,
separated by commas. Ex.
‘code1’, ‘code2’.
jackpots query no (^) boolean|
true or false
Adding jackpots info to game
info.
title query no string (32) Game title to search for.
title__contains query no (^) string (64) Searches for games that
contain passed string in their
title.
title__contains! query no (^) string (64) Searches for games that do
not contain passed string in
their title.
gamecategoryId query no (^) string (16) Searches for games that
contain passed string in their
title.
providerId query no string (16) Game category’s public ID
that searched games should
be assigned to.
labelsId__in query no (^) string (64) Game label’s codes separated
by commas.
jpCurrency query no (^) string (3) Currency code for Jackpot.
isFreebetSupport
ed
query no (^) boolean|
true or false
It is true, if searched games
should support freebets.


Copyright © 2025 Skywind Holdings Ltd.

Name Located In Required Type Notes

```
isBonusCoinsSup
ported
(deprecated)
```
query no (^) boolean|
true or false
It is true, if searched games
should support bonus coins.
transferEnabled query no (^) boolean |
true or false
It is true, if searched games
should support transfer.
isGRCGame query no (^) boolean |
true or false
It is true, if searched games
should be GRC.
jackpotTypes query no string (32) It is ‘true’ or comma-
separated list of jackpot
types, if searched games
should have jackpots.
The System responds to such request according to the following schema:
**Response**
Code Schema Example
200 [^
{
code: string (32),
title: string (32),
type: string (16),
limits: {
currencyCode: {
maxTotalStake: number,
stakeAll: Array of all possible
stake [number],
stakeDef: number,
stakeMax: number,
stakeMin: number,
winMax: number,
}
},
features: {
isFreebetSupported: boolean,
isMultibet: boolean,
isFunModeNotSupported: boolean,
gamble: boolean,
baseRTP: number,
highestPrizeProbability: number,
baseRTPRange: {
min: number,
max: number
},
jpRTP: number,
currenciesSupport: Array of
supported currency for the game
[string (3)],
gameFinalizationType: string
(32),
featuresRTP: {
mode: {
rtpReducer: boolean,
RTP: number
}

##### [

##### {

```
"code": "SX567",
"title": "Mr Monkey",
"type": "slot",
"limits": {
"USD": {
"maxTotalStake": 2000,
"stakeAll": [
1,
2,
3,
5
],
"stakeDef": 1,
"stakeMax": 5,
"stakeMin": 1,
"winMax": 200
},
"CNY": {
"maxTotalStake": 3000,
"stakeAll": [
2,
3,
5,
10
],
"stakeDef": 2,
"stakeMax": 10,
"stakeMin": 2,
"winMax": 400
}
},
"features": {
"isFreebetSupported": false,
"isMultibet": false,
"isFunModeNotSupported": false,
"gamble": false,
```

Copyright © 2025 Skywind Holdings Ltd.

```
Code Schema Example
}
},
releaseDate: string (32),
countries: Array list of available
countries codes ISO 3166- 2. If
empty– no restrictions [string (2)],
totalBetMultiplier: number,
jackpots: {
sw-jpgame: {
currency: string (3),
id: string (32),
pools: {
pool: {
amount:number
}
}
}
},
live: {
id: string (32),
provider: string (32),
dealer: {
name: string (32),
picture: string (1024)
},
status: string (32),
type: number
}
}
]
```
```
"baseRTP": 95,
"highestPrizeProbability": 100,
"baseRTPRange": {
"min": 90.42,
"max": 93.52
},
"jpRTP": 95,
"currenciesSupport": [
"EUR"
],
"gameFinalizationType": "none",
"featuresRTP": {
"mode1": {
"rtpReducer": true,
"RTP": 95.75
},
"mode2": {
"rtpReducer": true,
"RTP": 95.75
},
"freeGames": {
"rtpReducer": true,
"RTP": 95.75
}
}
},
"releaseDate": "2018- 08 -
22T12:28:51.382Z",
"countries": [
"CN",
"GB",
"FR"
],
"totalBetMultiplier": 20 ,
"jackpots": {
"sw-jpgame": {
"currency": "USD",
"id": "sw-jpgame",
"pools": {
"pool0": {
"amount": 122
}
}
}
},
"live": {
"id": "mock- 0 - 1",
"provider": "mock",
"dealer": {
"name": "Mock",
"picture":
"http://picture.com/mock.jpeg"
},
"status": "online",
"type": 0
}
}
]
```

Copyright © 2025 Skywind Holdings Ltd.

If any issues occur while getting a list of games, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
##### 400

```
40 Validation error
62 One of the parents is suspended
```
##### 401

```
10 Access Token is missing
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
403 206 Forbidden
```
```
404 51 Could not find entity
```
#### 5.7.1. Get Jackpot Tickers.............................................................................................................

**Request:** GET {jackpot-api.com}/ticker HTTP/1.1

**Domains:**

Europe EU Jackpot Ticker:

STG - [https://jpn-ticker-eu-gcp-str.ss211208.com/v1]

PROD - [https://jpn-ticker-gcp-str.sw420101.com/v1]

Asia AS Jackpot Ticker:

STG - [https://jpn-ticker-as-gcp-str.ss211208.com/v1]

PROD - [https://jpn-ticker-gcp-str.hep200512.com/v1]

The domains are dynamic and can be changed. Skywind Support Team will provide

domains on request.

```
Example of request
```
```
GET "{jackpot-api.com}/ticker?currency=EUR&jackpotIds=MY-
JP - POOL,MY-SECOND-JP-POOL" - H "accept: application/json"
```
The user can get information about jackpots by using tickers which are available for

games. This is a publicly available service and can be embedded into a client’s site or

portal.


Copyright © 2025 Skywind Holdings Ltd.

**Parameters to enter**

```
Name Located In Required Schema Notes
```
```
currency query yes string (3) Player’s currency.
```
```
jackpotIds query yes Array [string (32)]
```
```
Comma-separated array of
jackpot instance IDs. You
can get the jackpot
instance IDs by sending the
Get Game info request with
the gameCode of the of the
game for which you wish to
get the information on
Jackpot tickers.
```
The System responds to the parameters entered according to the following schema:

**Response**

```
Code Schema Example
```
```
200
[TickerInformationResponse
{
jackpotId: string,
jackpotType: string,
jackpotBaseType: string,
pools: {
JackpotPoolInformation{
amount: number
}
},
currency: string (3)
}
]
```
##### [

##### {

```
"jackpotId": "MAGNIFICENT-
SEVEN",
"jackpotType": "sw-the-
magnificent-seven",
"jackpotBaseType": "sw-
the-magnificent-seven",
"pools": {
"magnificent_seven": {
"amount": 106187.84
}
},
"currency": "EUR"
}
]
```
**Response parameters**

```
Name Description
```
```
jackpotId The unique ID of the jackpot entered in the request.
```
```
jackpotType The type of the jackpot defining its properties such as the properties of the
pools within this jackpot.
```
```
jackpotBaseType Jackpot type inherited by the jackpot type in this response.
```
pools (^) Pools this jackpot consists of.


Copyright © 2025 Skywind Holdings Ltd.

amount (^) The current amount of money in this jackpot pool.
currency (^) The currency in which the amount is displayed.
If any issues occur while getting the jackpot info, the System responds with the
following error codes:
HTTP Code Error Code Description
(^400 3) Validation error
(^500 1) JPN internal server error

### 5.8. Get Game Info

**Request:** GET api.operatorapi.com/games/{gameCode}/info HTTP/1.1

The user can get information (title, type, labels, provider information, etc.) about the

games available for its operator.

To do this, the game code should be entered according to the following schema:

**Parameters**

```
Name Located In Required Schema Notes
```
```
gameCode Path yes string (32)
Game code of a game that is
fetched.
jpCurrency Query no string (3) Currency code for Jackpot.^
```
```
addAggregatedFinalLimits Query no
```
```
boolean: “true” |
“false”
```
```
If true, it will return the
customized game limits of
the current operator,
otherwise it will return the
default game limits of the
game.
If currency is present, limits
will be return only for that
currency, otherwise – for all
currencies (it works only for
brands).
```
```
currency Query no string (3)
Currency code which is equal
to value.
```
The System responds to the parameters entered according to the following schema:

**Response**

```
Code Schema Example
200 {^
code: string (32),
title: string (32),
type: string (16),
limits: {
currencyCode: {
```
##### {

```
"code": "SX567",
"title": "Mr Monkey",
"type": "slot",
"limits": {
"USD": {
```

Copyright © 2025 Skywind Holdings Ltd.

```
Code Schema Example
maxTotalStake: number,
stakeAll: Array of all possible
stake [number],
stakeDef: number,
stakeMax: number,
stakeMin: number,
winMax: number,
}
},
features: {
isFreebetSupported: boolean,
isMultibet: boolean,
isFunModeNotSupported: boolean,
gamble: boolean,
baseRTP: number,
highestPrizeProbability: number,
baseRTPRange: {
min: number,
max: number
},
jpRTP: number,
currenciesSupport: Array of
supported currency for the game
[string (3)],
gameFinalizationType: string
(32),
featuresRTP: {
mode: {
rtpReducer: boolean,
RTP: number
}
}
},
releaseDate: string (32),
countries: Array list of available
countries codes ISO 3166- 2. If
empty– no restrictions [string (2)],
totalBetMultiplier: number,
jackpots: {
sw-jpgame: {
currency: string (3),
id: string (32),
pools: {
pool: {
amount:number
}
}
}
},
live: {
id: string (32),
provider: string (32),
dealer: {
name: string (32),
picture: string (1024)
},
status: string (32),
type: number
}
```
```
"maxTotalStake": 2000,
"stakeAll": [
1,
2,
3,
5
],
"stakeDef": 1,
"stakeMax": 5,
"stakeMin": 1,
"winMax": 200
},
"CNY": {
"maxTotalStake": 3000,
"stakeAll": [
2,
3,
5,
10
],
"stakeDef": 2,
"stakeMax": 10,
"stakeMin": 2,
"winMax": 400
}
},
"features": {
"isFreebetSupported": false,
"isMultibet": false,
"isFunModeNotSupported": false,
"gamble": false,
"baseRTP": 95,
"highestPrizeProbability": 100,
"baseRTPRange": {
"min": 90.42,
"max": 93.52
},
"jpRTP": 95,
"currenciesSupport": [
"EUR"
],
"gameFinalizationType": "none",
"featuresRTP": {
"mode1": {
"rtpReducer": true,
"RTP": 95.75
},
"mode2": {
"rtpReducer": true,
"RTP": 95.75
},
"freeGames": {
"rtpReducer": true,
"RTP": 95.75
}
}
},
"releaseDate": "2018- 08 -
22T12:28:51.382Z",
```

Copyright © 2025 Skywind Holdings Ltd.

```
Code Schema Example
} "countries": [
"CN",
"GB",
"FR"
],
"totalBetMultiplier": 20 ,
"jackpots": {
"sw-jpgame": {
"currency": "USD",
"id": "sw-jpgame",
"pools": {
"pool0": {
"amount": 122
}
}
}
},
"live": {
"id": "mock- 0 - 1",
"provider": "mock",
"dealer": {
"name": "Mock",
"picture":
"http://picture.com/mock.jpeg"
},
"status": "online",
"type": 0
}
}
```
If any issues occur while getting the game info, the System responds with the following

error codes:

```
HTTP Code Error Code Description
```
##### 400

```
40 Validation error^
```
##### 62

```
One of the parents has been
suspended
101 Not a brand^
```
##### 401

```
10 Access Token is missing
```
```
204 Access token error^
```
```
205 Access token has expired^
```
```
792 Access Session is expired
```
```
403 206 Forbidden^
```
##### 404

```
51 Could not find entity
```
```
213
```
```
Game \{gameCode\} is not
available for entity
300 Game not found^
```

Copyright © 2025 Skywind Holdings Ltd.

### 5.9. Get Player’s Game URL

**Request:**

GET api.operatorapi.com/players/{playerCode}/games/{gameCode} HTTP/1.1

The user can get a particular game URL for a specific player.

To do this, the game code, the player code (to get the URL for a particular player) and

the play mode (‘real’ by default) should be entered according to the following schema:

**Parameters to enter**

```
Name
Located
In
Required Schema
Notes
```
```
cashier query no string
```
```
The site of the cashier
```
```
playerCode path yes string (32)
```
```
Player’s code of a player that is
fetched.
```
```
gameCode path yes string (32)
Game code of a game that is
fetched.
```
```
ticket query yes/no string (64)
```
```
This is an external ticket with
customer data used to recognize
a customer in the provider’s
system.
For registered customers, tickets
can be provided either for real or
for fun play mode. This
parameter provides a quick
means of switching to real mode
via the mode button in a
game/lobby without redirecting
to merch_login_url.
This parameter is optional only
for fun play mode.
The ticket will be validated by the
API command: validate ticket.
type query no string (32) Type of the game.^
```
playmode query no

```
string | “fun”
or “real”
```
```
A play mode used for a ‘real’ or
‘fun’ game.
If the play mode is not specified,
the customer will be asked to
choose a play mode at the start
of the game.
```
```
language query no string (2)
```
```
The language used by a
customer.
If no language is defined, then a
language will be selected based
on customer data. If a customer
is anonymous, the language
must be defined.
lobby query no string The site^ of the lobby.^
```
```
ip query no string
```
```
It is used to determine the
player’s geographical location. If
an IP address is not set or the IP
is from the private space (like
```

Copyright © 2025 Skywind Holdings Ltd.

##### 10., 192.168., 172.[16-31].*),

```
restrictions are not used.
If the IP is present but the
location can't be determined, the
game launch is denied. Identified
location, used to control the
availability of the game for a
particular location.
If restrictions are not used on this
endpoint, player can get error on
real game launch if any
restriction is applied with real
player IP. Please do not send
your server IP.
```
```
aamsSessionId query no string
```
```
Optional parameter used only for
players under Italian regulation –
authority session id to provide in
case it’s needed to display it to
the player
```
```
aamsParticipationCode query no string
```
```
Optional parameter used only for
players under Italian regulation –
authority session (participation)
code to provide in case it’s
needed to display it to the player
```
The System responds to the parameters entered according to the following schema:

**Response**

```
Code Schema Example
200 {^
url: string (1024) – game URL
for specific player,
token: string (512) player
token to access game
}
```
##### {

```
"url":
"http://super_game.com/",
"token":
"oJqXX2tkAADKn3MpcM9kVbVk53neuI
YI62dEkYdYubl+9lyXRECjQww3VsmEP
fMoUkO6uqB56WDPhPGdS3aGnQ"
}
```
**Note:** The returned url is the URL that should be used for launching the game. The

returned token value can be ignored by the operator.

If any issues occur while retrieving the game URL, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
##### 400

```
40 Validation error
```
```
62 One of the parents is suspended
```
```
101 Not a brand
```
```
306 Game is suspended
```
```
703 IP address cannot be resolved
```
```
708 It is forbidden to start game from unauthorized site
```

Copyright © 2025 Skywind Holdings Ltd.

```
HTTP Code Error Code Description
```
```
712 Player is suspended
```
```
736 Entity is under maintenance, but maintenance url is not defined
```
```
751 Referrer is missing
```
##### 401

```
10 Access Token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
##### 403

```
206 User doesn’t have permission to execute the operation
```
```
701 Country [country] is restricted
```
```
702 Currency [currency] in country [country] restricted
```
##### 404

```
51 Could not find entity
```
```
102 Player is not found
```
```
213 Game is not available for entity|
```
```
240 Game is not found
```
```
502 Merchant not found
```
##### 500

```
506 Merchant internal error
```
```
700 Ip location lookup error
```
### 5.10. Get Fun Mode (Anonymous) Game URL

**Request:** GET api.operatorapi.com/fun/games/{gameCode} HTTP/1.1

The administrator can get a particular game URL for an anonymous player.

To do this, the game code should be entered according to the following schema:

**Parameter to enter**

```
Name Located In Required Schema Note
```
```
gameCode path yes string (32)
Game code of a game that is
fetched.
```
```
Ticket query no string (64)
```
```
This is an external ticket with
customer data used to recognise a
customer in the provider’s system.
For registered customers, tickets
can be provided either for real or for
fun play mode. This parameter
```

Copyright © 2025 Skywind Holdings Ltd.

```
provides a quick means of switching
to real mode via the mode button in
a game/lobby without redirecting to
merch_login_url.
The ticket will be validated by the
API command: validate ticket.
```
```
language query no string (2)
```
```
The language used by a customer.
If no language is defined, then a
language will be selected based on
customer data. If a customer is
anonymous, the language must be
defined.
```
```
merchantLoginUrl query no string (64)
Merchant’s login URL as encoded
string.
```
```
Ip query no string
```
```
It is used to determine the player’s
geographical location. If an IP
address is not set or the IP is from
the private space (like 10.,
192.168., 172.[16-31].*),
restrictions are not used.
If the IP is present but the location
can't be determined, the game
launch is denied. Identified
location, used to control the
availability of the game for a
particular location.
If restrictions are not used on this
endpoint, player can get error on
real game launch if any restriction
is applied with real player IP. Please
do not send your server IP.
```
The System responds to the parameters entered according to the following schema:

**Response**

```
Code Schema Example
```
```
200 {^
url: string (1024) – game URL,
token: string (512) –
anonymous player token to
access game
}
```
##### {

```
"url": "http://super_game.com/",
"token":
"oJqXX2tkAADKn3MpcM9kVbVk53neuIYI62
dEkYdYubl+9lyXRECjQww3VsmEPfMoUkO6u
qB56WDPhPGdS3aGnQ"
}
```
If any issues occur while retrieving the game URL, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
##### 400

```
40 Validation error
```
```
62 One of the parents has been suspended
```
```
101 This operation is only permitted for the brand
```

Copyright © 2025 Skywind Holdings Ltd.

```
HTTP Code Error Code Description
```
```
306 Game is suspended
```
```
902 Static domain is not defined
```
```
903 Dynamic domain is not defined
```
##### 401

```
10 Access Token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
##### 403

```
206 User doesn’t have permission to execute the operation
```
```
701 Country [country] is restricted
```
```
702 Currency [currency] in country [country] restricted
```
```
708 It is forbidden to start game from unauthorized site
```
##### 404

```
51 Could not find entity
```
```
85 Currency not found
```
```
102 Player not found
```
```
213 Game is not available for entity
```
```
240 Game not found
```
```
500 700 Ip location lookup error
```

Copyright © 2025 Skywind Holdings Ltd.

### 5 .11. Get Game History

**Request:** GET api.reportapi.com/history/game HTTP/1.1

The user can get game history for the key entity.

To do this, the following parameters should be entered according to the schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
offset query yes (^) integer Number of items to be
skipped from the beginning
of the selection.
Limit query yes (^) integer Number of items to return
(default – 20).
format query no string | “csv” Response’s format (CSV).
sortBy query no (^) string (32) Sorting key. Allowed values
are:
“brandId”,
“roundId”,
“playerCode”,
“gameCode”,
“currencyCode”,
“ts”,
“firstTs”,
“finished”,
“bet”,
“win”,
“revenue”,
“isTest”
sortOrder query no (^) string | “ASC” or
“DESC”
Sorting order: ASC or DESC.
roundId query no (^) string (32) A unique round number in
games.
roundId__in query no (^) string (32) This parameter allows to list
game rounds by comma.
playerCode query no (^) string (32) Player’s code of a player
that is fetched.
playerCode__in query no (^) string (64) Player’s code to search for,
separated by commas.
gameCode query no (^) string (32) Game’s code of a game that
is fetched.
gameCode__in query no (^) string (64) Game’s code to search for,
separated by commas.
currency query no (^) string (3) Player’s currency.
currency__in query no string (32) This parameter allows to list
currencies by comma.
firstTs query yes/no (^) string (32) Time stamp when game
was started in ISO 8601


Copyright © 2025 Skywind Holdings Ltd.

```
Name Located In Required Schema Notes
```
```
format (e.g. 2020 - 12 -
10T16:47:38.887Z)
firstTs __gt query yes/no string (32) Time stamp of the game to
compare in ISO 8601
format (e.g. 2020 - 12 -
10T16:47:38.887Z); gt –
greater than.
```
firstTs __gte query yes/no (^) string (32) Time stamp of the game to
compare in ISO 8601
format (e.g. 2020 - 12 -
10T16:47:38.887Z); gte –
greater than or equal.
firstTs __lt query yes/no (^) string (32) Time stamp of the game to
compare in ISO 8601
format (e.g. 2020 - 12 -
10T16:47:38.887Z); lt –
less than.
firstTs __lte query yes/no (^) string (32) Time stamp of the game to
compare in ISO 8601
format (e.g. 2020 - 12 -
10T16:47:38.887Z); lte –
less than or equal.
ts query yes/no (^) string (32) Time stamp when game
was finished in ISO 8601
format (e.g. 2020 - 12 -
10T16:47:38.887Z).
ts__gt query yes/no string (32) Time stamp when game
data was actually displayed
for comparison (gt –
greater than).
ts__gte query yes/no string (32) Time stamp when game
data was actually displayed
for comparison in ISO 8601
format (e.g. 2020 - 12 -
10T16:47:38.887Z); gte –
greater than or equal.
ts__lt query yes/no (^) string (32) Time stamp when game
data was actually displayed
for comparison in ISO 8601
format (e.g. 2020 - 12 -
10T16:47:38.887Z); lt –
less than.
ts__lte query yes/no (^) string (32) Time stamp when game
data was actually displayed
for comparison in ISO 8601
format (e.g. 2020 - 12 -
10T16:47:38.887Z); lte –
less than or equal.
finished query no (^) boolean| “true” or
“false”
Parameter for a broken
game status.
bet query no double Parameter that shows bet in
the game.
bet__lt query no (^) double Parameter which allows to
compare bets (lt – less
than).


Copyright © 2025 Skywind Holdings Ltd.

```
Name Located In Required Schema Notes
```
```
bet__lte query no double Parameter which allows to
compare bets (lte – less
than or equal).
```
bet__gt query no (^) double Parameter which allows to
compare bets (gt – greater
than).
bet__gte query no (^) double Parameter which allows to
compare bets (gte – greater
than or equal).
win query no (^) double Parameter that shows win
in the game.
win__lt query no double Parameter which allows to
compare wins (lt – less
than).
win__lte query no (^) double Parameter which allows to
compare wins (lte – less
than or equal).
win__gt query no (^) double Parameter which allows to
compare wins (gt – greater
than).
win__gte query no double Parameter which allows to
compare wins (gte –
greater than or equal).
revenue query no (^) double Parameter that shows
revenue of the game.
revenue__lt query no (^) double Parameter which allows to
compare revenue (lt – less
than).
revenue__lte query no double Parameter which allows to
compare revenue (lte – less
than or equal).
revenue__gt query no (^) double Parameter which allows to
compare revenue (gt –
greater than).
revenue__gte query no (^) double Parameter which allows to
compare revenue (gte –
greater than or equal).
device query no (^) string (64) Parameter that shows
device which is used in the
game.
balanceBefore query no (^) double This parameter shows the
balance which was before
the game starts.
balanceBefore__lt query no double Parameter allows to
compare balances which
were before the game
starts (lt – less than).
balanceBefore__lte query no (^) double Parameter allows to
compare balances which
were before the game
starts (lte – less than or
equal).
balanceBefore__gt query no (^) double Parameter allows to
compare balances which
were before the game
starts (gt – greater than).


Copyright © 2025 Skywind Holdings Ltd.

```
Name Located In Required Schema Notes
```
```
balanceBefore__gte query no double Parameter allows to
compare balances which
were before the game
starts (gte – greater than or
equal).
balanceAfter query no double This parameter shows the
balance after the game
ended.
```
balanceAfter__lt query no (^) double Parameter allows to
compare balances after the
game ended (lt – less than).
balanceAfter__lte query no (^) double Parameter allows to
compare balances after the
game ended (lte – less than
or equal).
balanceAfter__gt query no double Parameter allows to
compare balances after the
game ended (gt – greater
than).
balanceAfter__gte query no double Parameter allows to
compare balances after the
game ended (gte – greater
than or equal).
isTest query no (^) boolean This parameter allows to
make payment for test.
recoveryType query no string (16)
revert |
force-finish
Recovery type of a round to
search for.
recoveryType__in query no string (16) Recovery types of a round
to search for, separated by
commas.
The System responds to the parameters entered according to the following schema:
**Response**
Code Schema Example
200 [^
{
roundId: string (32),
brandId: string (32),
playerCode: string (32),
gameCode: string (32),
currency: string (3),
bet: number,
win: number,
credit: number,
debit: number,
revenue: number,
firstTs: string (32) (in
UTC time zone),
ts: string (32) (in UTC
time zone),
finished:boolean,
isTest: boolean ,
balanceBefore: number,
balanceAfter: number,

##### [

##### {

```
"roundId": "g6qQOB9X",
"brandId": "feE3Sb39",
"playerCode": "PLAYER1",
"gameCode": "sw_mrmnky",
"currency": "USD",
"bet": 0.1,
"win": 2,
"credit": 10,
"debit": 5,
"revenue": -1.9,
"firstTs": "2017- 07 -
14T07:07:01.080Z",
"ts": "2017- 07 -
14T07:07:11.930Z",
"finished": true,
"isTest": false,
"balanceBefore": 25000,
"balanceAfter": 24950,
```

Copyright © 2025 Skywind Holdings Ltd.

```
Code Schema Example
totalEvents: number,
totalJpWin: number,
totalContribution: number,
insertedAt: string (32),
device: string (255),
recoveryType: string (16):
[revert | force-finish]
}
]
```
```
"totalEvents": 1,
"totalJpWin": 1500,
"totalContribution":
0.0001824647,
"insertedAt": "2017- 07 -
14T07:07:12.092T",
"device": "web",
"recoveryType": "force-
finish"
}
]
Note :
Default output is limited to 20 entries per page. Max output is 100 entries per page.
CSV export is limited to 1 000 entries. Pagination is not currently available.
Data is available for the last 3 months.
‘Start date’ (firsts or insertedAt) and ‘End date’ (ts) (can be defined with modifiers), must be set.
Parameter ‘ts’ for unfinished rounds will be defined as ‘null’.
```
```
‘Debit and credit’ parameters change the balance, and they are not connected with the game’s ‘win
and bet’.
Debit amount means the transfer-in amount, and similar operations. Credit amount defines the
transfer-out amount, tournament win, etc. Please remember that transfer operations are deprecated.
‘recoveryType’ – this column shows a recovery type for the unfinished or broken rounds that were
resolved manually.
```
Please see the description of the fields from the Response:

```
Field Type Description
```
```
finished boolean
```
```
Time when the round has ended.
Can be true or false.
```
```
revenue number Amout of the bet minus the win.
```
```
currency string Currency code with the format ISO 4217.
```
```
debit number
```
```
Change of the balance which is not connected with
game’s win/bet. Debit amount means the transfer-in
amount, and similar operations.
```
```
credit number
```
```
Change of the balance which is not connected with
game’s win/bet. Credit amount stands for the transfer-
out amount, tournament win, etc.
```
```
roundId string A unique round number in games
```
```
brandId string Brand public id
```
```
playerCode string Player’s code
```
```
gameCode string Game code
```

Copyright © 2025 Skywind Holdings Ltd.

```
device enum
Type of the device which was used for the current
operation
```
```
win number Game win amount
```
```
bet number Game bet amount
```
```
balanceBefore number Player’s balance before the current operation
```
```
balanceAfter number Player’s balance after the current operation
```
```
totalEvents number Count of events in the rounds
```
```
firstTs string
```
```
Time of the first action in the round with the format ISO
8601 timestamp, in UTC time zone.
```
```
ts string
```
```
Time of the last action in the round with the format ISO
8601 timestamp (in UTC time zone)
```
```
isTest boolean Whether the round has been created only for testing
```
```
insertedAt string
```
```
Time when the round was saved in the format ISO 8601
timestamp.
```
```
recoveryType enum
Shows a recovery type for the unfinished or broken
rounds that were resolved manually.
```
```
totalJpContribution number Contribution of the bet which goes to the JackPot
```
```
totalJpWin number Game JackPot win amount
```
If any issues occur while retrieving the game history, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
##### 400

```
40 Validation error
```
##### 62

```
One of the parents has been
suspended
```
```
101
This operation is only permitted
for the brand
```
##### 401

```
10 Access Token is missing^
```
```
204 Access token error^
```
```
205 Access token has expired^
792 Access Session is expired^
```
##### 403 206

```
User doesn’t have permission to
execute the operation
```
```
404 51 Could not find entity^
```

Copyright © 2025 Skywind Holdings Ltd.

#### 5.11.1. Get Game History from 3 rd Party Game Providers

**Request:** GET api.operatorapi.com/history/external HTTP/1.1

This method enables the user to get external win/bet history from a specified key

entity, from the 3rd party game providers.

To do this, the following parameters should be entered according to the schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
currency query no (^) string (3) Player’s currency.
offset query yes (^) integer Number of items to be skipped
from the beginning of the
selection.
limit query yes integer Number of items to return
(default – 20).
sortBy query no (^) string (32) Sorting key (default is
“insertedAt”). Allowed values
are:
“insertedAt”
“brandId”,
“roundId”,
“eventId”,
“playerCode”,
“gameCode”,
“currency,
“finished”,
“bet”,
“win”,
“isTest”,
“isTest”,
“extTrxId”,
“gameProviderCode”,
“balanceBefore”
“balanceAfter”
“recoveryType”
sortOrder query no (^) string | “ASC” or
“DESC”
Sorting order: ASC or DESC.
(the default is descending)
roundId query no (^) string (32) A unique round number in
games.
extTrxId
query no (^) string (32) An external reference Id.
gameProviderCode query no (^) string (32) Game Provider’s code.
insertedAt query no (^) string ( 32 ) This parameter shows the time
when the spin was saved to the


Copyright © 2025 Skywind Holdings Ltd.

```
Name Located In Required Schema Notes
```
```
database ISO 8601 format (and
there can be a slight delay from
the time when the spin actually
happened).
It should be used for
incremental updates when
querying the API.
insertedAt__gt query no string (32) This parameter shows the time
when the spin was saved to the
database (and there can be a
slight delay from the time when
the spin actually happened).
It should be used for
incremental updates when
querying the API.
(gt – greater than). It has ISO
8601 format.
```
insertedAt__lt query no (^) string ( 32 ) This parameter shows the time
when the spin was saved to the
database (and there can be a
slight delay from the time when
the spin actually happened).
It should be used for
incremental updates when
querying the API.
(lt – less than). It has ISO 8601
format.
playerCode query no (^) string (3 2 ) Player’s code of a player that is
fetched.
gameCode query no string (32) Game’s code of a game that is
fetched.
isTest query no (^) boolean This parameter allows to make
payment for test.
The System responds to the parameters entered according to the following schema:
**Response**
Code Schema Example
200 [^
{
extTrxId: string (32),
bet: number,
win: number,
currency: string (3),
brandId: string (32),
playerCode: string (32),
roundId: string (32),
gameCode: string (32),
gameProviderCode: string (32),
balanceBefore: number,
balanceAfter: number,
isTest: boolean,
revenue: number,
insertedAt: string (32) in UTC
time zone,

##### [

##### {

```
"extTrxId": "99167",
"bet": 10,
"win": 20,
"currency": "USD",
"brandId": "Qa1weSD",
"playerCode": "PL0001",
"roundId": 167,
"gameCode": "pt_gm1",
"gameProviderCode":
"sw_gos",
"balanceBefore": 1000,
"balanceAfter": 1010,
"revenue": 10,
"isTest": false,
```

Copyright © 2025 Skywind Holdings Ltd.

```
Code Schema Example
providerSpecificData: number
}
]
```
```
"insertedAt": "2019- 02 -
12T11:32:15.000Z",
"providerSpecificData": null
}
]
```
If any issues occur while getting external win/bet history, the System responds with

the following error codes:

```
HTTP Code Error Code Description
```
##### 400

```
11 Token is missed
```
```
12 Token is not valid
13 Token is expired
```
### 5.12. Get Game History Round Details

**Request:** GET api.operatorapi.com/history/game/{roundId} HTTP/1.1

This method enables the user to get game round details from a specified key entity.

To do this, the following parameters should be entered according to the schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
```
currency query no string (3) Player’s currency.
```
offset query yes (^) integer Number of items to be
skipped from the beginning
of the selection.
limit query yes (^) integer Number of items to return
(default – 20).
sortBy query no string (32) Sorting key (default is
“insertedAt”).
Allowed values are:
“insertedAt”
“brandId”,
“roundId”,
“eventId”,
“playerCode”,
“gameCode”,
“currency,
“finished”,
“bet”,
“win”,
“isTest”,


Copyright © 2025 Skywind Holdings Ltd.

```
Name Located In Required Schema Notes
“isTest”,
```
```
“extTrxId”,
“gameProviderCode”,
“balanceBefore”
“balanceAfter”
“recoveryType”
```
sortOrder query no (^) string | “ASC” or
“DESC”
Sorting order: ASC or DESC.
(the default is descending)
roundId query no (^) string (32) A unique round number in
games.
extTrxId
query no (^) string (32) An external reference Id.
gameProviderCode query no (^) string (32) Game Provider’s code.
insertedAt query no (^) string (32) This parameter shows the
time when the spin was
saved to the database in
ISO 8601 format. There can
be a slight delay from the
time when the spin actually
happened.
It should be used for
incremental updates when
querying the API.
insertedAt__gt query no string (32) This parameter shows the
time when the spin was
saved to the database in
ISO 8601 format. There can
be a slight delay from the
time when the spin actually
happened.
It should be used for
incremental updates when
querying the API.
(gt – greater than).
insertedAt__lt query no (^) string (32) This parameter shows the
time when the spin was
saved to the database in
ISO 8601 format. There can
be a slight delay from the
time when the spin actually
happened.
It should be used for
incremental updates when
querying the API.
(lt – less than).
playerCode query no (^) string (32) Player’s code of a player
that is fetched.
gameCode query no string (32) Game’s code of a game that
is fetched.
isTest query no (^) boolean This parameter allows
making test payments.
win query no double Shows game wins.


Copyright © 2025 Skywind Holdings Ltd.

```
Name Located In Required Schema Notes
```
```
win__lt query no double Allows comparing wins.
(lt – less than).
win__gt query no double Allows comparing wins.
(gt – greater than).
finished query no boolean| “true” or
“false”
```
```
Parameter for a broken
game status.
bet query no double Shows game bets.
bet__lt query no double Allows comparing bets.
(lt – less than).
bet__gt query no double Allows comparing bets.
(gt – greater than).
currency query no string (3) Player’s currency.
balanceBefore query no double Shows the balance before
game starts.
balanceBefore__lt query no double Allows comparing balances
from before the game start.
(lt – less than).
balanceBefore__gt query no double Allows comparing balances
from before the game start.
(gt – greater than).
balanceAfter query no double Shows the balance after the
game ended.
balanceAfter__lt query no double Allows comparing balances
after the game ended.
(lt – less than).
balanceAfter__gt query no double Allows comparing balances
after the game ended.
(gt – greater than).
recoveryType query no string (16)
revert |
force-finish
```
```
Recovery type of a round to
search for.
```
The System responds to such requests according to the following schema:

**Response**

```
Code Schema Example
200 [^
{
spinNumber: integer,
type: string (16),
currency: string (3),
bet: number,
win: number,
credit: number,
debit: number,
balanceAfter: number,
balanceBefore: number,
endOfRound: boolean,
ts: string (32) in UTC
time zone,
test: boolean,
isPayment: boolean,
totalJpWin: number,
totalContribution: number
}
]
```
##### [

##### {

```
"spinNumber": 0,
"type": "slot",
"currency": "CNY",
"bet": 0,
"win": 0,
"credit": 10,
"debit": 5,
"balanceBefore": 1000,
"balanceAfter": 3644,
"endOfRound": true,
"ts": "2019- 03 - 01T14:57:39.780Z",
"test": false,
"isPayment": true,
"totalJpContribution": 0,
"totalJpWin": 0
}
]
```

Copyright © 2025 Skywind Holdings Ltd.

If any issues occur while getting game round details, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
##### 400

```
40 Validation Error^
```
```
62 One of the parents is suspended^
```
```
101 Not a brand^
```
##### 401

```
10 Access Token is missing^
```
```
204 Access token error^
```
```
205 Access Token is expired^
```
```
792 Access Session is expired^
```
##### 403 206

```
User doesn’t have permission to
execute the operation
```
```
404 51 Could not find entity^
```
#### 5.12.1. Get Game History Round Details from 3 rd Party Game Providers

**Request:** GET api.operatorapi.com/history/external/{roundId}/details HTTP/1.1

This method enables the user to get game round details from a specified key entity.

To do this, the following parameters should be entered according to the schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
```
roundId path yes
string (32)
|integer
```
```
A unique round number in
games.
```
gameProviderCode query yes (^) string (32) Game Provider’s code.
extTrxId query yes (^) string (32) An external reference Id.
The System responds to the parameters entered according to the following schema:
**Response**
Code Schema Example
200 string^
" <html>History</html>"
If any issues occur while getting game round details, the System responds with the
following error codes:


Copyright © 2025 Skywind Holdings Ltd.

```
HTTP Code Error Code Description
```
##### 400

```
11 Token is missed
```
```
12 Token is not valid
```
```
13 Token is expired
```
### 5.13. Get Game Spin History for the Brand

**Request:** GET api.reportapi.com/history/spins HTTP/1.1

This method enables returning information per spin for the brand.

To do this, the following parameters should be entered according to the schema:

```
Name Located In Required Schema Notes
```
offset query yes (^) integer Number of items to be
skipped from the beginning
of the selection.
limit query yes (^) integer Number of items to return
(default – 20).
sortBy query no (^) string (32) Sorting key. Allowed values
are:
“brandId”,
“roundId”,
“playerCode”,
“gameCode”,
“currencyCode”,
“firstTs”,
“finished”,
“bet”,
“win”,
“isTest”
sortOrder query no string | “ASC” or
“DESC”
Sorting order: ASC or DESC.
playerCode query no (^) string (3 2 ) Player’s code of a player that
is fetched.
playerCode__in query no string (64) Player’s code to search for,
separated by commas.
gameCode query no (^) string (32) Game’s code of a game that
is fetched.
gameCode__in query no (^) string (64) Game’s code to search for,
separated by commas.


Copyright © 2025 Skywind Holdings Ltd.

insertedAt__gt query no (^) string (32) This parameter shows the
time when the spin was
saved to the database (and
there can be a slight delay
from the time when the spin
actually happened).
It should be used for
incremental updates when
querying the API.
(gt – greater than). It has
ISO 8601 format.
insertedAt__lt query no (^) string (32) This parameter shows the
time when the spin was
saved to the database (and
there can be a slight delay
from the time when the spin
actually happened).
It should be used for
incremental updates when
querying the API.
(lt – less than). It has ISO
8601 format.
ts__gt query no string (32) This parameter shows the
time when the spin
happened.
(gt – greater than). It has
ISO 8601 format.
ts__lt query no string (32) This parameter shows the
time when the spin
happened.
(lt – less than). It has ISO
8601 format.
type query no (^) string (32) This parameter shows game
type equal to value as spin,
shot, respin, slot, freegame,
jackpot-mini-game, jackpot-
win, play-mini-game,
freebet, etc.
type__in query no (^) string (32) This parameter shows the
game type from a comma
separated list as spin, shot,
respin, slot, freegame,
jackpot-mini-game, jackpot-
win, play-mini-game,
freebet, etc.
**Types of parameters and their description**

- System events:
    **force-finish** – the event when the round has been finished manually by the
    support team
    **finalized** – the event when the round has been closed because of the
    expiration time
    **revert-game** – the event when the game state and the payment were
    reverted


Copyright © 2025 Skywind Holdings Ltd.

- Slot Games Events:
    **slot** – the event for the spin in some slot games
    **spin** – the event for the spin in games
- Bonus and promotions:
    **bonusgame** – the event when the player triggers a bonus game
    **freebet** – the event for the free bet mode when the player produces a bet
    action
    **freegame** – the event when the player triggers a free game mode
    **respin** – the event for a free (0 bet) spin
- Jackpot events:
    **jackpot-mini-game** – the event when the player triggers a jackpot mini
    game
    **jackpot-win** – the event for a jackpot win
    **jackpot_win** – an old event for a jackpot win
**mini-game** – the event when the player triggers a mini game
**play-mini-game** – spin events which are produced inside mini games

The System responds to such requests according to the following schema:

**Response**

```
Code Schema Example
200 {^
roundId: string (32),
spinNumber: integer,
type: string (32),
gameId: string (32),
playerCode: string ( 255 ),
currency: string (3),
bet: number,
win: number,
endOfRound: boolean,
ts: string (32),
test: boolean,
credit: number,
debit: number,
balanceBefore: number,
balanceAfter: number,
totalJpContribution:
number,
totalJpWin: number,
insertedAt: string (32) in
UTC time zone
}
```
##### [

##### {

```
"roundId": "Rk8VenwB",
"spinNumber": 200000134,
"type": "slot",
"gameId": "sw_sland",
"currency": "USD",
"bet": 0.1,
"win": 1.5,
"endOfRound": true,
"ts": "2017- 02 -
16T16:37:13.613Z",
"test": false,
"credit": 10,
"debit": 5,
"balanceBefore": 10,
"balanceAfter": 5,
"totalJpContribution": 10,
"totalJpWin": 5,
"insertedAt": "2017- 02 -
16T16:37:13.613Z"
}
]
Note:
```
```
‘Debit and credit’ parameters change the balance, and they are not connected with game’s ‘win and
bet’.
Debit amount means the transfer-in amount, and similar operations. Credit amount stands for the
transfer-out amount, tournament win, etc. Please remember that transfer operations are deprecated.
The ‘test’ parameter defines if the player is a test player. True – indicates a test player, false – indicates
a real player.
```

Copyright © 2025 Skywind Holdings Ltd.

If any issues occur while returning the information per spin, the System responds

with the following error codes:

```
HTTP Code Error Code Description
```
##### 400

```
40 Validation error
```
```
62
One of the parents is
suspended
101 Not a brand
```
##### 401

```
10 Access Token is missing
```
```
204 Access token error
```
```
205 Access Token has expired
```
```
792 Access Session is expired
```
##### 403

```
50 Not master entity
```
```
206 Forbidden
```
```
404 51 Could not find entity^
```
### 5.14. Get Game History Round Details SmResult

**Request:** GET api.operatorapi.com/history/game/{roundId}/sm-result HTTP/1.1

This method allows the user to get the game history round details formatted as

sm_result which is required by Portuguese regulation.

To do this, the round id should be entered according to the schema:

**Parameter to enter**

```
Name Located In Required Schema Notes
```
```
roundId path yes string (32)
```
```
A unique round number in
games.
```
The System responds to the parameters entered according to the following schema:

**Response**

```
Code Schema Example
200 {
smResult: string (4000)
}
```
##### {

```
"smResult":
"0:2;3;4;2;3#2;5;5;2;3#0;7;6;4;3#"
}
```
If any issues occur while getting game round details, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
##### 400

```
40 Validation Error^
```
```
62 One of the parents is suspended^
```

Copyright © 2025 Skywind Holdings Ltd.

```
HTTP Code Error Code Description
```
```
101 Not a brand^
```
##### 401

```
10 Access Token is missing^
```
```
204 Access token error^
```
```
205 Access Token is expired^
```
```
792 Access Session is expired^
```
```
403 206 Forbidden^
404 51 Could not find entity^
```
### 5.15. Get Game History Round Details Visualisation................................................................

**Request:** GET api.operatorapi.com/history/game/{roundId}/image HTTP/1.1

This method enables the user to get the game history round details visualisation.

To do this, the following parameters should be entered according to the schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
```
roundId path yes
```
```
string (32)
|integer
```
```
A unique round number in
games.
```
```
language query no string (2)
```
```
If specified, the game history
information will be provided in
the specified language
```
```
timezone query no string (32)
```
```
If specified, the game history
information will be converted
to the specified timezone, for
example, “Asia/Shanghai”
```
The System responds to such requests according to the following schema:

**Response**

```
Code Schema Example
200 {^
imageUrl: string,
ttl: number
}
```
##### {

```
"imageUrl":
"http://example.com/gamehistory/0.0.1/his
tory.html?data=bhESknjk578.eyJlSWQiJZCI6N
jk1NzCwiiZXhwIjoxNTMwNzgzNNJKNCDCjYzLCSSv
dXAifQ&url=site.com&language=en",
"ttl": 3600
}
```
**ttl** – Token expires after the specified period (in seconds).

Below is the example of the image available via the URL in the response:


Copyright © 2025 Skywind Holdings Ltd.

If any issues occur while getting game round details, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
##### 400

##### 40

```
Validation Error
```
##### 62

```
One of the parents is suspended
```
```
101 Not a brand^
```
##### 401

```
10 Access Token is missing^
```
```
204 Access token error^
```
```
205 Access Token is expired^
```
```
792 Access Session is expired^
```
##### 403 206

```
User doesn’t have permission to
execute the operation
```
##### 404

```
51 Could not find entity^
683 Game History details not found^
901 Domain is used by entity^
409 689 Game history URL not found^
```
### 5.16. Get Game Event Details Visualisation

**Request:** GET

api.operatorapi.com/history/game/{roundId}/details/{spinNumber}/image HTTP/1.1

This method enables the user to return a URL to a visualisation of game history with

spin details.

To do this, the following parameters should be entered according to the schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
```
roundId path yes
string (32)
|integer
```
```
A unique round number in
games.
```
```
spinNumber path yes integer
```
```
Number of spins per game
round.
```
```
language query no string (2)
```
```
If it is specified, visualisation
will be translated to given
language.
Note: for Chinese localization
the language code should
```

Copyright © 2025 Skywind Holdings Ltd.

```
include 5 symbols – zh-cn,
zh- tw
```
The System responds to such requests according to the following schema:

**Response**

```
Code Schema Example
200 {^
imageUrl:string
(65536)
}
```
##### {

```
"imageUrl":
"https://flc.cdnsky.net/gamehistory/2.9.5/histo
ry.html?data=eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVC
J9.eyJyb3VuZElkIjozNDk1M..."
}
```
Below is the example of the image available via the URL in the response:

If any issues occur while returning a URL, the System responds with the following

error codes:

```
HTTP Code Error Code Description
```
```
400 40 Validation Error^
```
##### 401

```
10 Access Token is missing^
```
```
204 Access token error^
```
```
205 Access token has expired^
```
```
792 Access Session is expired^
```
```
403 206
User doesn’t have permission to
execute the operation
```
```
404
```
```
51 Could not find entity^
683 Game history details not found^
689 Game history URL not found^
900 Domain does not exist^
```

Copyright © 2025 Skywind Holdings Ltd.

### 5.17. Get Currency Report.......................................................................................................

**Request:** GET api.reportapi.com/report/wallet/currency HTTP/1.1

This method enables the user to get a report on currencies available for the operator.

To generate the report, the following parameters should be entered according to the

schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
```
includeSubBrands query no
```
```
boolean: “true” |
“false”
```
```
It is false by default. If true,
all current entity children’s
data will be included in the
response.
```
```
From
query
no integer
```
```
Period start date in UNIX-time
```
- seconds that have elapsed
since 00:00:00 1 January
1970 UTC.

```
To query no integer
```
```
Period end date in UNIX-time
```
- seconds that have elapsed
since 00:00:00 1 January
1970 UTC.

```
Ts query yes string (32)
```
```
Time stamp when game was
finished, and it is always
required.
```
```
ts__gt query yes/no string (32)
```
```
This parameter shows the
time when the spin
happened.
(gt – greater than).
```
```
ts__gte query yes/no string (23)
```
```
This parameter shows the
time when the spin happened
in ISO 8601 format (e.g.
2020 - 12 - 10T16:47:38.887Z);
gte – greater than or equal.
```
```
ts__lt query yes/no string (32)
```
```
This parameter shows the
time when the spin happened
in ISO 8601 format (e.g.
2020 - 12 - 10T16:47:38.887Z);
lt– less than.
```
```
ts__lte query yes/no string (32)
```
```
This parameter shows the
time when the spin happened
in ISO 8601 format (e.g.
2020 - 12 - 10T16:47:38.887Z);
lte – less than or equal.
currency (currency
equal to value)
query no string (3)
```
```
Parameter that shows player’s
currency in the game.
currency_in (currencies
separated by commas)
query no string (32)
```
```
This parameter allows to list
currencies by comma.
```
```
limit query no integer
```
```
Number of items to return
(default – 20).
```
**Note:** The range between ts__gte and ts__lte dates cannot be more than 1 month, and ts__gte date
in ts__gte and ts__lte range cannot be older than 3 months. If only ts__gte is specified and it is older
than 1 month, it will be automatically set to 1 month ago from the current date.
The option ‘yes/no’ means that the operator needs to choose one of them.


Copyright © 2025 Skywind Holdings Ltd.

The System responds to such requests according to the following schema:

**Response**

```
Code Schema Example
200 [^
{
currency: string (3),
playedGames: integer,
bets: number,
winnings: number,
ggr: number,
betsUsd: number,
winningsUsd: number,
ggrUsd: number
}
]
```
##### [

##### {

```
"currency": "USD",
"playedGames": 112503,
"bets": 256432.12,
"winnings": 548123.34,
"ggr": 804555.46,
"betsUsd": 256432.12,
"winningsUsd": 548123.34,
"ggrUsd": 804555.46
}
]
```
If any issues occur while getting the report, the System responds with the following

error codes:

```
HTTP Code Error Code Description
```
##### 400

```
40 Validation error^
```
```
62 One of the parents is suspended^
```
```
101 Not a brand^
```
##### 401

```
10 Access Token is missing^
```
```
204 Access token error^
```
```
205 Access token has expired^
```
```
792 Access Session is expired^
```
##### 403 206

```
User doesn’t have permission to
execute the operation
```
```
404 51 Could not find entity^
```

Copyright © 2025 Skywind Holdings Ltd.

### 5 .18. Get Player Report

**Request:** GET api.reportapi.com/report/players HTTP/1.1

This method enables the user to get a report on a player’s information and activity.

To get this information under the key entity, the following parameters should be

entered according to the schema:

**Parameters to enter**

```
Name
Located
In
Required Schema
Notes
```
format (^) query no string (8) Response’s format (CSV).
currency (^) query no string (3) Player’s currency.
currency__in
query no string (32)
This parameter allows to list
currencies by comma.
playerCode
query no string (32)
Player’s code of a player that
is fetched.
playerCode__in
query no string (64)
Player’s code to search for,
separated by commas.
gameCode
query no string (32)
Game’s code of a game that
is fetched.
gameCode__in
query no string (64)
Game’s code to search for,
separated by commas.
playedGames__gt
query no number
This parameter allows to
compare amounts of played
games (gt – greater than).
playedGames__lt
query no number
This parameter allows to
compare amounts of played
games (lt – less than).
playedGames__gte
query no number
This parameter allows to
compare amounts of played
games (gte – greater than or
equal).
playedGames__lte
query no number
This parameter allows to
compare amounts of played
games (lte – less than or
equal).
playedGames
query no number
This parameter shows
amounts of played games.
totalBets__gt
query no number
This parameter allows to
compare total bets in the
game (gt – greater than).
totalBets__lt
query no number
This parameter allows to
compare total bets in the
game (lt – less than).
totalBets__gte
query no number
This parameter allows to
compare total bets in the
game (gte – greater than or
equal).
totalBets__lte
query no number
This parameter allows to
compare total bets in the
game (lte – less than or
equal).


Copyright © 2025 Skywind Holdings Ltd.

```
Name
Located
In
Required Schema
Notes
```
```
totalBets
query no number
This parameter shows total
bets in the game.
totalWins__gt
query no number
```
```
This parameter allows to
compare total wins in the
game (gt – greater than).
totalWins__lt
query no number
```
```
This parameter allows to
compare total wins in the
game (lt – less than).
totalWins__gte
query no number
```
```
This parameter allows to
compare total wins in the
game (gte – greater than or
equal).
totalWins__lte
query no number
```
```
This parameter allows to
compare total wins in the
game (lte – less than or
equal).
totalWins
query no number
This parameter shows total
wins in the game.
paymentDate
query yes string (32)
```
```
This parameter shows the
date when the payment was
made.
paymentDate__gt
query yes string (32)
```
```
This parameter allows to
compare payment dates (gt
```
- greater than).
paymentDate__lt
query yes string (32)

```
This parameter allows to
compare payment dates (lt –
less than).
paymentDate__gte
query yes string (32)
```
```
This parameter allows to
compare payment dates (gte
```
- greater than or equal).
paymentDate__lte
query yes string (32)

```
This parameter allows to
compare payment dates (lte
```
- less than or equal).
paymentDateHour
query yes string (32)

```
This parameter shows the
date and time when the
payment was made.
paymentDateHour__gt
query yes string (32)
```
```
This parameter allows to
compare payment’s values of
date and time (gt – greater
than).
paymentDateHour__lt
query yes string (32)
```
```
This parameter allows to
compare payment’s values of
date and time (lt – less
than).
paymentDateHour__gte
query yes string (32)
```
```
This parameter allows to
compare payment’s values of
date and time (gte – greater
than or equal).
paymentDateHour__lte
query yes string (32)
```
This parameter allows to
compare payment’s values of
date and time (lte – less than
or equal).
**Note** : The range between paymentDateHour__gte and paymentDateHour__lte dates cannot be more
than 1 month, and paymentDateHour__gte date in paymentDateHour__gte and paymentDateHour__lte
range cannot be older than 3 months.


Copyright © 2025 Skywind Holdings Ltd.

If only paymentDateHour__gte is specified and it is older than 1 month, it will be automatically set to 1
month ago from the current date.

The System responds to such requests according to the following schema:

**Response**

```
Code Schema Example
```
```
200 PlayersReport [^
Inline Model 1
]
Inline Model 1 {
playerCode: string (32),
currency: string (3),
playedGames: number,
totalBets: number,
totalWins: number,
totalJpWins: number,
totalFreebetWins: number,
GGR: number,
RTP: number,
debits: number,
credits: number
}
```
##### [

##### {

```
"playerCode": "PL0001",
"currency": "USD",
"playedGames": 27182,
"totalBets": 1216851546,
"totalWins": 2851546,
"totalJpWins": 15000,
"totalFreebetWins": 2300,
"GGR": 1546,
"RTP": 0.93123741241,
"debits": 1500,
"credits": 10000
}
]
```
```
Note :
‘debits’ – is a total sum (in player’s currency) of player’s ‘transfer-in’ operations, which is applicable
for action games (such like fu-fish)
‘credits’ – is a total sum (in player’s currency) of ‘transfer-out’ (action games), tournament win,
GRC redeem operations, etc.
Please mind that action games are deprecated.
‘totalFreebetWins’ – is a total amount of money the player won with the Free Bets feature.
```
```
Debits and credits are not related to in-game bets and wins, but, as described above, they are the
result of operations that affect the player’s balance.
```
If any issues occur while getting the report, the System responds with the following

error codes:

```
HTTP Code Error Code Description
```
##### 400

```
40 Validation error
```
```
62
One of the parents is
suspended
```
```
101 Not a brand
```
##### 401

```
10 Access Token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
##### 403 206

```
User doesn’t have permission to
execute the operation
```
(^404 51) Could not find entity


Copyright © 2025 Skywind Holdings Ltd.

### 5 .19. Get List of Countries

**Request:** GET api.operatorapi.com/countries HTTP/1.1

The user can get a list of countries its operator can perform in.

The System responds to such requests according to the following schema:

**Response**

```
Code Schema Description
200 [
{
```
##### [

##### {

```
"displayName": "Bangladesh",
"code": "BD"
},
{
"displayName": "China",
"code": "CN"
}
]
```
```
displayName: string (64),
```
```
code: string (2)
```
##### }

##### ]

If any issues occur while retrieving a list of countries, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
##### 401

```
10 Access Token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
```
403 206
User doesn’t have permission to
execute the operation
404 51 Could not find entity
```
### 5. 20. Get List of Currencies

**Request:** GET api.operatorapi.com/currencies HTTP/1.1

The user can get a list of currencies available for its operator.

The System responds to such requests according to the following schema:

**Response**

```
Code Schema Description
200 [
{
[
{
"displayName": "US Dollar",
"code": "USD"
},
```
```
displayName: string
(64),
code: string ( 3 )
(ISO 4217)
```

Copyright © 2025 Skywind Holdings Ltd.

##### }

##### ]

##### {

```
"displayName": "Chinese Yuan",
"code": "CNY"
}
]
```
If any issues occur while retrieving the list of currencies, the System responds with the

following error code

```
HTTP Code Error Code Description
```
##### 401

```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
```
403 206 Forbidden
```
```
404 51 Could not find entity
```
### 5.21. Get List of Languages

**Request:** GET api.operatorapi.com/languages HTTP/1.1

The user can get a list of languages supported by its operator.

The System responds to such requests according to the following schema:

**Response**

```
Code Schema Description
200 [^
{
name: string (32),
nativeName: string
(32),
direction: string
(3),
code: string (2) (ISO
639 - 1)
}
]
```
##### [

##### {

```
"name": "English",
"nativeName": "English",
"direction": "ltr",
"code": "en"
}
]
```
If any issues occur while getting the list of languages, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
##### 401

```
10 Access Token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```

Copyright © 2025 Skywind Holdings Ltd.

```
403 206 User doesn’t have permission to
execute the operation
```
```
404 51 Could not find entity
```
### 5.22. Get Jackpots

**Request:** GET api.operatorapi.com/jackpots HTTP/1.1

This method returns to the merchant and its brand a list of jackpots for the current

entity, which includes:

- Must Win Jackpots (MWJP)
- Active jackpots
- Ended jackpots that are displayed for 5 days

To get this information, the following parameters should be entered according to the

schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
```
jackpotIds query no
string Comma separated jackpot ids
to return (default – all).
```
```
gameCodes query no string
```
```
Comma separated game
codes, that belong to entity,
for which jackpot instances
should be returned.
```
The System responds to such requests according to the following schema:

**Response**

```
Code Schema Example
```
(^200) [
{
id: string (32),
externalId: string (32),
name: string (32),
currency: string (3),
type: string (32),
startDate: string (32),
externalStartDate: string (32),
status: number,
endDate: string (32),
poolsCount: number,
jackpotPools: Array [
{
poolId: string (32),
contributionPercent: number,
initialSeed: number,
tickerAmount: number

##### [

##### {

```
"id": "sw_jackpot_id",
“externalId”: "JPN1_7198e85d",
"name": "sw_jackpot_name",
"currency": "EUR",
"type": "MWJP",
"startDate": "202 3 - 09 - 15 T12:45:42.324Z",
"externalStartDate": "2023- 09 -
15T13:27:38.368Z",
"status": 1,
"endDate": "202 3 - 02 - 23T12:45:42.324Z",
"poolsCount": 3,
"jackpotPools": [
{
"poolId": "small",
"contributionPercent": 0.2,
"initialSeed": 10,
```

Copyright © 2025 Skywind Holdings Ltd.

##### }

##### ]

##### }

##### ]

```
"tickerAmount": 1000
}
]
}
]
```
```
Notes:
```
- **type** parameter possible values are "Game Level" and "MWJP".
- **status** parameter possible values are 1 – for active jackpot, 0 – for closed one, 1 will be
    returned in most cases.
- Greece gaming requlation requires the **externalId** and **externalStartDate** parameters to
    be sent each time a jackpot is won. Whenever this happens, a new **Jackpot ID** (field
    "externalId") is assigned under the same **Jackpot Provider ID** (field "id").

If any issues occur while returning the list of jackpots, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
(^403 2) Operation forbidden
(^500 1) JPN internal server error

### 5 .23. Promotions

All methods in this chapter can be used for managing Free Bets promotions.

#### 5.23.1. Create a Promotion...........................................................................................................

**Request** : POST api.operatorapi.com/promo HTTP/1.1

This method can be used if the Operator decides to create and manage Free Bets via

FIS instead of their own system.

**JSON Request example (Free Bets)**

```
Schema Example
```
```
{
title: string (32),
startDate: string (32),
endDate: string (32),
type: string (16),
rewards: array [{
freebetAmount: number,
games: [{
gameCode: string (32),
coins: [{
[Currency]: { coin: number }
}]
}]
```
##### {

```
"title": "Quick freebet promo",
"startDate": "2018- 12 - 10T15:00:00.000Z",
"endDate": "2018- 12 - 10T16:00:00.000Z",
"type": "freebet",
"rewards": [{
"freebetAmount": 15,
"games": [{
"gameCode": "sw_fazer6",
"coins": [{ "USD": { "coin": 0.01
}}, {"CNY": { "coin": 0.1 } }]
}]
}]
```

Copyright © 2025 Skywind Holdings Ltd.

```
Schema Example
```
```
}]
}
providerSpecificOptions?: {
type: 'bets' | 'infinity' |
'sure_win'; // Bonus type
duration: number; // Duration in
seconds. Only required for infinity
bonus.
ttl: number; // Bonus time to
live (after it has been started by the
player) in seconds.
}
}
```
##### }

```
" providerSpecificOptions": {
"type": "infinity",
"duration": 120 ,
"ttl": 86400
}
}
```
**Note: providerSpecificOptions** is an optional parameter used to create promotions

with games from external game providers. It has 3 properties: **type** , **ttl** and

**duration** , currently available only for PatePlay games.

Bonus bets are of 3 types:

1. **bets** – This is a regular free bets bonus awarding a specific amount of bonus
    bets to the player at a specific stake.
2. **infinity** - This is a free bets bonus that awards the player with an infinite
    amount of bonus bets (at a rate of maximum 2 bets per second) for a limited
    period (ie: 30 seconds).
3. **sure_win** – This is a free bet bonus that allows the player to spin infinitely until
    they win anything. The bonus ends on the first bet with payout > 0.

If no bet type is provided, the default value will be **bets**.

If no ttl is provided, the default value will be **60*60*24 (1 day)**.

In case some features are not supported by the external game provider, Skywind will

try to convert the promo to a type supported by the game provider and return an

error if not possible.

**Parameter to enter**

```
Name Located In Required Schema Notes
```
```
info body yes object Promotion parameters.
```
**Info**

```
Name Required Schema Notes
```
```
title yes string (32) The title of the promotion
```

Copyright © 2025 Skywind Holdings Ltd.

status yes (^) string | active or
inactive
Assigns the specified status to the
promotion
type yes string: freebet Defines the type of the promotion, for
example, freebet
conditions no (^) object Conditions under which the promotion
will be created for the player.
startDate yes (^) string (32) Start date in ISO 8601 timestamp
(e.g. 2020- 12 - 10T16:47:38.887Z)
startRewardOnGameOpen no (^) boolean If true, starts the promo the moment
the game is launched.
timezone no (^) string (32) Time zone according to which the
start and end date will be applied.
endDate yes string (32) End date in ISO 8601 timestamp (e.g.
2020 - 12 - 10T16:47:38.887Z)
description no (^) string (32) The description of the promotion
intervalType no (^) string (32) Promo calculation period. Possible
values: hourly | daily | weekly |
monthly
daysOfWeek no (^) Array [string (3) ] Days of week when the promotion will
run. For example, [ "FRI", "SAT" ]
daysOfMonth no Array [string (2) ] Days of month when the promotion
will run. For example, [ "1", "2", "3",
"29", "30" ]
customerIds no (^) Array [string (3 2 ) ] A comma-separated list of customer
IDs, to which the promotion will be
applied. For example, [ "p_code1",
"p_code2" ]
externalId no (^) string (32) The ID assigned to the promo in the
Operator system. See note below the
table.
rewards yes (^) Array The array for specifying the promotion
parameters for one or more promotion
types
expirationPeriod no number After the specified number of
days/hours from the start of the
promotion the Free Bets will expire.
expirationPeriodType no (^) string | daily or
hourly
The time unit that will be used for
specifying the expirationPeriod
parameter above
games yes (^) array List of codes of the games where the
promotion will run
freebetAmount yes number The number of free bets awarded to a
player in this promotion
**Note:** externalId parameter is used only in the cases when the Operator also needs to duplicate the
promo in their own system. The Operator should then specify the external ID in the request. This way
when Skywind sends a promo transaction to the Operator’s wallet, the Operator will receive it with
their external ID rather than the ID assigned by Skywind system.
**Response**
Code Schema Example


Copyright © 2025 Skywind Holdings Ltd.

##### 201 {^

```
title: string (32),
status: string (32),
type: string (32),
conditions: {
"and": [
null
],
"or": [
null
],
value: number,
operator: string (32),
valueField: string (32)
},
startDate: string (32),
timezone: string (32),
endDate: string (32),
description: string (32),
intervalType: string (32),
daysOfWeek: Array [string
(32)],
daysOfMonth: Array [string
(32)],
timeOfDay: string (32),
customerIds: Array [string
(32)],
externalId: string (32),
rewards: [
"Single reward type
should be used in this array.
So, use one and remove the
others.",
{
freebetAmount: 10,
expirationPeriod: 5,
expirationPeriodType:
daily,
games: [
{
gameCode:
string(32),
coins: [Currency]:
{ "coin": number },
```
```
}
]
}
```
##### {

```
"title": "Christmas promo",
"status": "active",
"type": "freebet",
"conditions": {
"and": [
null
],
"or": [
null
],
"value": 99,
"operator": "<=",
"valueField":
"deposit_amount"
},
"startDate": "2019- 12 -
23T15:00:00.000Z",
"startRewardOnGameOpen": false,
"timezone":
"America/Los_Angeles",
"endDate": "2019- 12 -
24T16:00:00.000Z",
"description": "Some
description of this promo",
"intervalType": "weekly",
"daysOfWeek": [
"FRI",
"SAT"
],
"daysOfMonth": [
"1",
"2",
"3",
"29",
"30"
],
"timeOfDay": "12:30:00",
"customerIds": [
"p_code1",
"p_code2"
],
"externalId": "extPromoId",
"id": "pQ3513OE",
"createdUserId": "aZQ900OE",
"createdUserName": "USER1",
"updatedUserId": "aZQ900OE",
"createdAt": "2019- 12 -
23T15:00:00.000Z",
"updatedAt": "2019- 12 -
23T16:00:00.000Z",
"everStarted": true,
"active": true,
"archived": true,
"brandId": "s0GBK8gK",
"owner": "operator",
"rewards": [
"Single reward type should be
used in this array. So, use one
and remove the others.",
{
```

Copyright © 2025 Skywind Holdings Ltd.

```
"freebetAmount": 10,
"expirationPeriod": 5,
"expirationPeriodType":
"daily",
"games": [
{
"gameCode": "sw_gsxr",
"coins": [
{
"USD": {
"coin": 0.01
}
},
{
"CNY": {
"coin": 0.1
}
}
]
},
{
"gameCode": "sw_er6f",
"coins": [
{
"USD": {
"coin": 0.2
}
},
{
"CNY": {
"coin": 0.3
} } ] } ] } ] }
```
If any issues occur while creating the promotion, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
```
400 40 Validation error
```
##### 401

```
10 Access Token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
##### 403 206

```
User doesn’t have permission to
execute the operation /
Forbidden
```
```
404 684 Referenced. Item is not found
```

Copyright © 2025 Skywind Holdings Ltd.

#### 5.23.2. Add Promo Rewards for Player

**Request** : PUT api.operatorapi.com/players/{playerCode}/promo/{promoId} HTTP/1.1

This method enables the merchant to enable specified promotions for the specified

players.

**JSON Request Example**

```
Schema Example
```
```
{
amount: number,
expirationPeriod: number,
expirationPeriodType: string
}
```
##### {

```
"amount": 10,
"expirationPeriod": 1,
"expirationPeriodType": "daily"
}
```
**Parameters to enter**

```
Name Located In Required Schema Notes
```
amount body yes (^) number Count of rewards to be
added to the player
(free bets etc.)
expirationPeriod body no (^) number The interval during
which the reward is
prolonged (based on
expirationPeriodType)
expirationPeriodType body no (^) string Time interval type:
hourly, daily, weekly,
monthly
playerCode path Yes (^) string (32) The unique ID of player
promoId path yes (^) string (32) Public ID of the
promotion
The System responds to this type of requests according to the following schema:
**Response**
Code Schema Example
200 [
{
promoId: string (32),
status: string,
expireAt: string (32),
playerCode: string (32)
}
]

##### [

##### {

```
"promoId": "W4RkGRen",
"status": "pending",
"expireAt": "2019- 12 -
23T15:00:00.000Z",
"playerCode": "PLAYER001"
}
```

Copyright © 2025 Skywind Holdings Ltd.

##### ]

If any issues occur while getting the promotions, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
##### 400

```
40 Validation error
```
```
709 Player has no such promo
```
##### 710

```
Can’t execute operation for
promo with its type
```
```
711 Promo is not in a valid state
```
##### 401

```
10 Access Token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
##### 403 206

```
User doesn’t have permission to
execute the operation /
Forbidden
```

Copyright © 2025 Skywind Holdings Ltd.

#### 5.23.3. Add Players to the Promotion

**Request** : POST api.operatorapi.com/promo/{promoId}/players HTTP/1.1

This method allows you to add up to 1 000 players per request to the existing

promotion.

**Parameters to enter**

```
Name Located In Required Schema Notes
```
```
promoId Path yes string (32) The unique ID of the promotion
```
playersCodes Body yes (^) Array [string
(32)]
Array of players codes. The
maximum number of players is 1
000.
The System responds to the request according to the following schema:
**Response**
Code Schema Example
200 {^
playerCode1: string (32),
playerCode2: string (32)
}

##### {

```
"playerCode1": "OK",
"playerCode2": "Promotion
already added"
}
```
If any issues occur while updating the promotion, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
##### 401

```
10 Access Token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
##### 403

##### 206

```
User doesn’t have permission to
execute the operation /
Forbidden
```
##### 706

```
Can not update promo that is
not pending
```
```
404 680 Promotion not found
```

Copyright © 2025 Skywind Holdings Ltd.

#### 5. 23 .4. Get a Promotion by ID

**Request** : GET api.operatorapi.com/promo/{promoId} HTTP/1.1

This method enables the merchant to get information on a specific promotion by its

ID.

To get this information, the following parameters should be entered according to the

schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
```
promoId path yes string The unique ID of the
promotion
```
```
includeTotals query no boolean |
true or false
```
```
If true, the response
will contain the ‘players
participated’ and ‘total
payout’ fields
```
The System responds to this type of requests according to the following schema:

**Response**

```
Code Schema Example
```
```
200 {^
title: string (32),
status: string (32),
type: string (32),
conditions: {
"and": [
{
"and": [
null
],
"or": [
null
],
value: number,
operator: string
(32),
valueField:
deposit_amount |
deposit_count
}
```
```
startDate: string (32),
startRewardOnGameOpen:
boolean,
timezone: string,
endDate: string,
description: string,
intervalType: hourly |
daily | weekly | monthly
daysOfWeek: [string (3)],
daysOfMonth: [string (2)],
timeOfDay: string,
```
##### {

```
"title": "Christmas promo",
"status": "active",
"type": "freebet",
"conditions": {
"and": [
{
"and": [
null
],
"or": [
null
],
"value": 99,
"operator": "<=",
"valueField":
"deposit_amount"
}
],
"or": [
{
"and": [
null
],
"or": [
null
],
"value": 99,
"operator": "<=",
"valueField":
"deposit_amount"
}
```

Copyright © 2025 Skywind Holdings Ltd.

```
customerIds: [string],
externalId: string,
id: string,
createdUserId: string,
createdUserName: string,
updatedUserId: string,
createdAt: string,
updatedAt: string,
everStarted: boolean,
active: boolean,
archived: boolean,
brandId: string,
owner: string,
rewards: [
"Single reward type
should be used in this array.
So, use one and remove the
others.",
{
freebetAmount: number,
expirationPeriod:
number,
expirationPeriodType:
hourly | daily | weekly |
monthly,
games: [
{
gameCode: string
coins: [Currency]:
{ "coin": number }
}
]
}
]
}
```
##### ],

```
"value": 99,
"operator": "<=",
"valueField":
"deposit_amount"
},
"startDate": "2019- 12 -
23T15:00:00.000Z",
"startRewardOnGameOpen": false,
"timezone":
"America/Los_Angeles",
"endDate": "2019- 12 -
24T16:00:00.000Z",
"description": "Some
description of this promo",
"intervalType": "weekly",
"daysOfWeek": [
"FRI",
"SAT"
],
"daysOfMonth": [
"1",
"2",
"3",
"29",
"30"
],
"timeOfDay": "12:30:00",
"customerIds": [
"p_code1",
"p_code2"
],
"externalId": "extPromoId",
"id": "pQ3513OE",
"createdUserId": "aZQ900OE",
"createdUserName": "USER1",
"updatedUserId": "aZQ900OE",
"createdAt": "2019- 12 -
23T15:00:00.000Z",
"updatedAt": "2019- 12 -
23T16:00:00.000Z",
"everStarted": true,
"active": true,
"archived": true,
"brandId": "s0GBK8gK",
"owner": "operator",
"rewards": [
"Single reward type should be
used in this array. So, use one
and remove the others.",
{
"freebetAmount": 10,
"expirationPeriod": 5,
"expirationPeriodType":
"daily",
"games": [
{
"gameCode": "sw_gsxr",
"coins": [
{
"USD": {
```

Copyright © 2025 Skywind Holdings Ltd.

```
"coin": 0.01
}
},
{
"CNY": {
"coin": 0.1
}
}
]
},
{
"gameCode": "sw_er6f",
"coins": [
{
"USD": {
"coin": 0.2
}
},
{
"CNY": {
"coin": 0.3
} } ] } ] } ] }
```
If any issues occur while getting the promotions, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
##### 401

```
10 Access Token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
##### 403 206

```
User doesn’t have permission to
execute the operation /
Forbidden
```
```
404 680 Promotion not found
```

Copyright © 2025 Skywind Holdings Ltd.

#### 5.23.5. Get a List of Promotions...................................................................................................

**Request** : GET api.operatorapi.com/promo HTTP/1.1

This method enables the merchant to get a list of promotions.

To get this information, the following parameters should be entered according to the

schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
offset query no (^) integer Number of items to be
skipped from the
beginning of the
selection (default – 0).
limit query no (^) integer Number of items to
return (default – 20).
sortBy query no (^) string (32) Sorting fields. Allowed
values are ‘code’, ‘title’,
‘providerTitle’,
‘providerCode’,
‘categoryList’.
sortOrder query no (^) string (4) Sorting order: ASC or
DESC.
archived query no (^) boolean |
true or false
If specified, gets
promotions with given
archived value (default

- false)

enabled query no (^) boolean |
true or false
If specified, gets
promotions with the
enabled status
status query no string | active or
inactive
If specified, get
promotions with given
status.
state__in query no (^) string | in_progress,
pending, finished or
expired
List of promotion states.
startDate__gte query no (^) string (32) Start date in ISO 8601
timestamp (e.g. 2016 -
12 - 10T16:47:38.887Z);
gt – greater than.
startDate__lte query no (^) string (32) Start date in ISO 8601
timestamp (e.g. 2016-
12 - 10T16:47:38.887Z);
lt-less than
endDate__gte query no string (32) End date in ISO 8601
timestamp (e.g. 2016 -
12 - 10T16:47:38.887Z);
gt – greater than
endDate_ _lte query no (^) string (32) End date in ISO 8601
timestamp (e.g. 2016-
12 - 10T16:47:38.887Z);
lt - less than


Copyright © 2025 Skywind Holdings Ltd.

createdAt__gte query no (^) string (32) Creation date in ISO
8601 timestamp greater
than or equal.
createdAt__lte
query no (^) string (32) Creation date in ISO
8601 timestamp less
than or equal.
createdAt__gt query no string (32) Creation date in ISO
8601 timestamp greater
than.
createdAt__lt query no (^) string (32) Creation date in ISO
8601 timestamp less
than.
title__ contains query no (^) string (32) This parameter
indicates that the
promo name contains
the specified text.
labelsId__in query no string (32) The response will
include the promotions
that include at least one
of the specified labels
type query no string The response will
include the promotions
of the specified types.
format query no (^) string | csv The only available
format is CSV
includeTotals query no (^) boolean |
true or false
If true, the response
will contain ‘total
participated’ and ‘total
payouts’ fields
includeGames query no boolean |
true or false
If true, the response
will return a game list
owner query no (^) string (32) |
skywind, operator
Filters promotions by
the owner
externalId query no string (32) Returns a list of
promotions with the
specified externalId.
**Note:** externalId parameter is used only in the cases when the Operator also needs to duplicate the
promo in their own system. The Operator should then specify the external ID in the request. This way
when Skywind sends a promo transaction to the Operator’s wallet, the Operator will receive it with
their external ID rather than the ID assigned by Skywind system.
**Response**
Code Schema Example
200 [
{
id: string (32),
title: string (32),
type: string (32),
status: Array [string
(32)],
state: Array [string
(32)]
brandId: string (32),
brandPath: string (32),
startDate: string (32),
endDate: string (32),

##### [

##### {

```
"id": "pQ3513OE",
"title": "Christmas promo",
"type": "freebet",
"status": "inactive",
"state": "pending",
"brandId": "bRaNd0o1",
"brandPath": "ENT1:BRAND1",
"startDate": "2019- 12 -
23T15:00:00.000Z",
"endDate": "2019- 12 -
24T16:00:00.000Z",
```

Copyright © 2025 Skywind Holdings Ltd.

```
timezone: string (32),
description: string (32),
intervalType: string
(32),
daysOfWeek: Array [string
(32)],
daysOfMonth: Array
[string (32)],
timeOfDay: string (32),
createdUserId: string
(32),
createdDate: string (32),
everStarted: boolean,
}
]
```
(^)
"timezone":
"America/Los_Angeles",
"description": "Some
description of this promo",
"intervalType": "weekly",
"daysOfWeek": [
"FRI",
"SAT"
],
"daysOfMonth": [
"1",
"2",
"3",
"29",
"30"
],
"timeOfDay": "12:30:00",
"createdUserId": "aZQ900OE",
"createdDate": "2018- 12 -
23T16:00:00.000Z",
"everStarted": true,
"externalId": "extPromoId"
}
]
If any issues occur while getting the report, the System responds with the following
error codes:
HTTP Code Error Code Description
400 40 Validation error

##### 401

```
10 Access Token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
##### 403 206

```
User doesn’t have permission to
execute the operation /
Forbidden
```

Copyright © 2025 Skywind Holdings Ltd.

#### 5.23.6. Get Number of Free Bets for a Player

**Request:**

GET api.operatorapi.com/players/{playerCode}/promo/{promoId}/freeBetLeft

HTTP/1.1

This method enables the merchant to get the number of free bets left on a specific

promotion for a specific player.

**Parameters to enter**

```
Name Located In Required Schema Notes
```
```
promoId path yes string The unique ID of the promotion.
```
**playerCode** path yes (^) string (32) The unique ID of the player.
The System responds to this type of requests according to the following schema:
Code Schema Example
200 {^
"promoId": string (32),
"status": string,
"expireAt": string,
"playerCode": string,
"freeBetLeft": number
}

##### {

```
"promoId": "W4RkGRen",
"status": "pending",
"expireAt": "20 23 - 12 - 23T15:00:00.000Z",
"playerCode": "PLAYER001",
"freeBetLeft": 2
}
```
In case of issues, the System responds with the following error codes:

```
HTTP Code Error Code Description
```
```
400 709 Player has no such promo (description)
```
##### 401

```
10 Access token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
##### 403 206

```
User doesn’t have permission to execute the operation /
Forbidden
```
```
404 680 Promotion not found
```

Copyright © 2025 Skywind Holdings Ltd.

#### 5.23.7. Delete Player from the Promotion

**Request** : DELETE api.operatorapi.com/players/{playerCode}/promo/{promoId}

HTTP/1.1

This method lets you delete the player from a promotion. If you need to delete a

player from the promotion in Finished and Running statuses, you must set the force

parameter to true.

**Parameters to enter**

```
Name Located In Required Schema Notes
```
force query yes* (^) boolean Forces to reset the
promo for the specified
player regardless of the
promo status.
playerCode path yes (^) string (32) The unique ID of the
player.
promoId path yes (^) string (32) The unique ID of the
promotion.
***Note:** if the **force** parameter isn’t sent in the request or is sent but set to **false** ,
the player will be deleted from the promotion only if the promotion is in Pending
status.
**Response**
Code Example
204 Promo rewards for player was reset
If any issues occur while archiving the promotion, the System responds with the
following error codes:
HTTP Code Error Code Description

##### 400

```
40 Validation error
```
```
709 Player has no such promo
```
##### 710

```
Can’t execute operation for
promo with its type
```
```
711 Promo is not in a valid state
```
##### 401

```
10 Access Token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```

Copyright © 2025 Skywind Holdings Ltd.

```
HTTP Code Error Code Description
```
##### 403 206

```
User doesn’t have permission to
execute the operation /
Forbidden
```

Copyright © 2025 Skywind Holdings Ltd.

#### 5.23.8. Archive a Promotion

**Request** : DELETE api.operatorapi.com/promo/{promoId} HTTP/1.1

This method enables the merchant to finish a specific promotion by specifying its ID.

This method can be used only for the promotions in Pending status.

To do this, the following parameters should be entered according to the schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
promoId path yes (^) string The unique ID of the
promotion
includeTotals query no (^) boolean |
true or false
If true, the response
will contain the ‘players
participated’ and ‘total
payout’ fields
The System responds to such requests with the following message:
**Response**
Code Example
(^204) Successfully archived promotion
If any issues occur while archiving the promotion, the System responds with the
following error codes:
HTTP Code Error Code Description

##### 401

```
10 Access token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
##### 403

##### 206

```
User doesn’t have permission to
execute the operation /
Forbidden
```
##### 706

```
Can not update promo that is
not pending
```
```
404 680 Promotion not found
```

Copyright © 2025 Skywind Holdings Ltd.

### 5.24. Change Player Nickname

**Request: PATCH api.operatorapi.com/players/change-nickname HTTP/1.1**

This method allows you change player’s nickname.

To update the nickname, the following parameters should be entered according to the

schema:

**Parameters to enter**

```
Name Locate
d in
```
```
Require
d
```
```
Schema Example
```
```
Info body Yes
ChangeNicknameData
{
code: string (32),
nickname: string (15)
}
```
```
{
"code": "PL0001",
"nickname": "Name"
}
```
The System responds to such requests with the following message:

**Response**

```
Code Example
```
```
204 Nickname has been changed successfully
```
***Note: If you do not have permission to use this method, please contact**

**the Support team**

If any issues occur while updating the nickname, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
```
400 62 One of the parents is suspended
```
```
101 Not a brand
```
```
401 10 Access token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
```
403 206 User doesn’t have permission to execute the operation /
Forbidden
```
```
404 51 Could not find entity
```
```
102 Player not found
```

Copyright © 2025 Skywind Holdings Ltd.

## 6. Round Finalization

To reduce the number of unfinished games in the system, **Skywind recommends**

**enabling an automatic round finalization** – **automatic closure of the game**

**rounds that are still open after the period of time that is specified by the**

**operator**.

## Round Finalization can also be referred to as Round Reconciliation and Round Auto-

closure.

From the game perspective, there are 3 types of round finalization, each game has

one finalization type assigned to it:

- Autoplay finalization – each remaining feature is randomly played out by the
    server and the awarded prizes are paid out.
- Guaranteed payment finalization
    - remaining feature with guaranteed prizes (for example, Cash Prize
       Bonus – each next pick guarantees to award a prize) is randomly
       played out by the server and the awarded prizes are paid out.
    - remaining feature with non-guaranteed prizes (for example, Free
       Games - each next free spin does not guarantee to award any prize) is
       force finished (round is finished and remaining feature is not played out
       by the server).
- None – the game does not support automatic finalization
    (autoplay/guaranteed payment) or does not require finalization.

From the server side perspective, the operator can pick a period of time after which

the rounds are eligible for finalization (usually, the value is between hours and days)

and choose from the following solutions that are available for automatic round

finalization:

#### 6.1. Offline Payment

**Request:** POST api.playerapi.com/api/credit HTTP/1.1

**Request:** POST api.playerapi.com/api/debit HTTP/1.1

In this case, the Skywind system will try to play out the remaining bonus feature for

the player (randomly) and pay them the prize. This approach is recommended in

case the operator supports payments for offline players (can accept payments with

expired **cust_session_id** ).

For example, if a player left the game in the middle of a free spins round, and the

game supports auto-play finalization, the remaining free spins will be auto-played

and for each one of them an appropriate /credit and /debit (with 0-bet) requests will

be sent.

As another example, if the player played a cashpot game and some prize was left in

the cashpot, this money will be paid to the player during the automatic game


Copyright © 2025 Skywind Holdings Ltd.

finalization.

Please note that 0 - bets are bets generated for the free spin rounds, bonus rounds or

other player actions, such as the cashpot collection.

Note, that in the /credit requests which are sent during round finalization, the last

player’s "cust_session_id" is used (which may be expired) and additional field

"is_finalization_payment": true is added.

In case the offline payment request is sent to the operator wallet, the operator’s

system should validate the player’s round and, if applicable, validate that the

"cust_session_id" sent by Skywind, although it might be expired, is the same

token which was used in the corresponding/latter credit request.

In case the round has a pending transaction, this transaction will be retried as a part

of the finalization, but if the retry has failed, the round will stay open and not be

finalized.

**Example of JSON Request**

```
Example
{
... same fields as in 2.4.1 Debit customer
and 2.4.3. Credit Customer
"is_finalization_payment": true
}
```
#### 6.2. Round Statistics

**Request** : POST api.playerapi.com/api/resolve_round HTTP/1.1

In this case, Skywind will try to play out the remaining bonus feature for the player

(randomly) and instead of calling a payments API, it will calculate the total bet and

total win of the round (that is "round statistics") and send this data to the

/resolve_round endpoint. The operator will then align round results in their own

system according to Skywind round statistics. This option is more preferable as it can

also resolve pending payments which were not resolved as part of the online/offline

retry mechanism. For example, if there is a pending win in Skywind system (Skywind

does not know if the win transaction succeeded or failed on the operator side), with

the round statistics mechanism Skywind will consider the pending bet/win

transactions to be successful and add it to the total bet/win of the round. The

operator will need to reconcile the round in their system accordingly.

Note that in the /resolve_round request which is sent during round finalization, the

player’s **cust_session_id** is not used (because it may be expired), an additional field

**is_finalization_payment** : true is added and the **event_type** field is set to "round-

statistics".


Copyright © 2025 Skywind Holdings Ltd.

**Example of JSON Request**

```
Example
{
"merch_id": "EB",
"merch_pwd": "SomePassword",
"cust_id": "1234",
"round_id": "3YAo10j3",
"game_id": 17733709,
"event_type": "round-statistics",
"game_code": "sw_mrmnky",
"total_bet": 1,
"total_win": 2,
"total_jp_contribution": 0.00001,
"trx_id": "trx",
"currency_code": "USD",
"timestamp": 1000000,
"game_status": "settled",
"is_finalization_payment": true,
"total_jp_win": 1500,
"sm_result": "0:2;3;4;2;3#2;5;5;2;3#0;7;6;4;3#"
}
```
**Response**

Code Schema Example

```
200 {^
error_code: number,
error_msg: string,
balance: number,
free_bet_count: number (optional),
messages: Array (optional)
}
```
##### {

```
"error_code": 0,
"balance": 2055.15
}
```
For Error Response details see Error Responses.


Copyright © 2025 Skywind Holdings Ltd.

#### 6.3. Force-Finish

**Request** : POST api.playerapi.com/api/resolve_round HTTP/1.1

Besides the two previous methods, there is also a less preferrable method of a so-

called force-finish round closure. It’s not recommended to use this method as it

closes the round as is, in a state where it was left by a player, which means that if a

player did not finish their free spins round or has a stuck win payment – it will ignore

those and just close the round.

**Note:** Although possible, this option is not recommended as a solution for

automated round closure and should be used only in cases when manual handling of

problematic rounds is needed.

Please note that when a round is force-finished (either by the automated process or

manually by our support team through our Back Office), a notification about the

round closure is not sent to the operator by default.

If a notification is required, please contact our Support Team to enable this option.

Once enabled, when a round is force-finished, a round statistics request will be sent

to the resolve_round endpoint (which you should implement). The request will look

the same as the one described in 6.2 Round Statistics with the only difference of

"event_type" field value equaling "force-finish".

**Example of JSON Request**

```
Example
{
"merch_id": "EB",
"merch_pwd": "SomePassword",
"cust_id": "1234",
"round_id": "3YAo10j3",
"game_id": 17733709,
"event_type": "force-finish",
"game_code": "sw_mrmnky",
"total_bet": 1,
"total_win": 2,
"total_jp_contribution": 0.00001,
"trx_id": "trx",
"currency_code": "USD",
"timestamp": 1000000,
"game_status": "settled",
"is_finalization_payment": true,
"total_jp_win": 1500
}
```

Copyright © 2025 Skywind Holdings Ltd.

**Response**

Code Schema Example

```
200 {^
error_code: number,
error_msg: string,
balance: number,
free_bet_count: number (optional),
messages: Array (optional)
}
```
##### {

```
"error_code": 0,
"balance": 2055.15
}
```

Copyright © 2025 Skywind Holdings Ltd.

## APPENDIX

### Test Account Provision

For testing purposes, the merchant should register at least **2 test customers** (when

test_cust=true) manually in the merchant’s System. After the registration has been

performed, the merchant should send the provider the login details and passwords of

the test users.


Copyright © 2025 Skywind Holdings Ltd.

## APPENDIX

### Reports API

To export the reports with increased limits by using the number of returned responses,

you can use a specific service. These are the available endpoints:

GET api.reportapi.com/v1/history/game

GET api.reportapi.com/v1/report/wallet/currency

GET api.reportapi.com/v1/report/players

Their patterns are fully identical with those methods, which are described in the

document, but their limits by the number of returned responses are incresed to

1 000 000.


Copyright © 2025 Skywind Holdings Ltd.

## APPENDIX

### Jackpot Contribution Reports

Jackpot contribution is a part of the stake taken to fund a jackpot pool. In fact,

contribution can be divided into parts to fund JP pool and seed.

There is a set of methods, which allows to return a report with a list of jackpot

contributions for a specified game by merchant and its brands.

#### 1. Get Jackpot Contribution Report

**Request:** GET api.reportapi.com/report/jackpot/contributions HTTP/1.1

This method enables the merchant and its brand to get a report with a list of jackpot

contributions for a specified game. All amounts are converted in euro.

To get this information, the following parameters should be entered according to the

schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
offset query no (^) integer Number of items to be skipped
from the beginning of the
selection.
limit query no (^) integer Number of items to return
(default – 20).
sortBy query no (^) string (32) Sorting key. Allowed values are:
“brandId”,
“roundId”,
“playerCode”,
“gameCode”,
“currencyCode”,
“ts”,
“ 114 venti”,
“finished”,
“bet”,
“win”,
“revenue”,
“isTest”
sortOrder query no (^) string (4) Sorting order: ASC or DESC.
dateHour query yes string (32) This parameter shows the date
and time when the contribution
was made, and it is always
required.
dateHour__gt query yes/no (^) string (32) This parameter allows to
compare contribution’s values of


Copyright © 2025 Skywind Holdings Ltd.

```
date and time (gt – greater
than).
```
dateHour__lt query yes/no (^) string (32) This parameter allows to
compare contribution’s values of
date and time (lt – less than).
dateHour__gte query yes/no string (32) This parameter allows to
compare contribution’s values of
date and time (gte – greater
than or equal).
dateHour__lte query yes/no (^) string (32) This parameter allows to
compare contribution’s values of
date and time (lte – less than or
equal).
gameCode query no (^) string (32) Game’s code of a game that is
fetched.
gameCode__in query no (^) string (64) Game’s code to search for,
separated by commas.
**Note:** The option ‘yes/no’ means that the operator needs to choose one of them.
The System responds to such requests according to the following schema:
**Response**
Code Schema Example
200 [^
{
dateHour: string (32),
gameCode: string (32),
currency: string (3),
seedAmount: number,
progressiveAmount: number,
totalBetAmount: number,
jpWinAmount: number,
totalBetCount: integer,
jpWinCount: integer,
firstActivity: string (32),
lastActivity: string (32),
seedWin: number,
progressiveWin: number
}
]

##### [

##### {

```
"dateHour": "2018- 01 -
03T17:00:00.000Z",
"gameCode": "pt-allad-reel-
jp",
"сurrency": "USD",
"seedAmount": 12.5,
"progressiveAmount": 6.5,
"totalBetAmount": 6.5,
"jpWinAmount": 15146.6,
"totalBetCount": 120,
"jpWinCount": 2,
"firstActivity": "2018- 01 -
03T17:10:01.020Z",
"lastActivity": "2018- 01 -
03T17:10:29.854Z",
"seedWin": 0,
"progressiveWin": 0
}
]
```

Copyright © 2025 Skywind Holdings Ltd.

If any issues occur while getting the report, the System responds with the following

error codes:

```
HTTP Code Error Code Description
```
##### 400

```
40 Validation Error
```
```
62
One of the parents is
suspended
```
```
101 Not a brand
```
##### 401

```
10 Access Token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
##### 403 206

```
User doesn’t have permission to
execute the operation
```
(^404 51) Could not find entity

#### 2. Get Players’ Jackpot Contribution Report

**Request:** GET api.reportapi.com/report/jackpot/contributions/players HTTP/1.1

This method enables the merchant and its brand to get a report with a list of jackpot

contributions for a specified game and players. All amounts are available in euro and

the player’s currency.

To get this information, the following parameters should be entered according to the

schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
```
offset query no integer Number of items to be
skipped from the beginning
of the selection.
```
```
limit query no integer Number of items to return
(default – 20).
```
sortBy query no (^) string (32) Sorting key. Allowed values
are:
“brandId”,
“roundId”,
“playerCode”,
“gameCode”,
“currencyCode”,
“ts”,
“ 116 venti”,


Copyright © 2025 Skywind Holdings Ltd.

```
“finished”,
“bet”,
“win”,
“revenue”,
“isTest”
```
sortOrder query no (^) string (4) Sorting order: ASC or DESC.
dateHour query yes (^) string (32) This parameter shows the
date and time when the
contribution was made, and it
is always required.
dateHour__gt query yes/no string (32) This parameter allows to
compare contribution’s
values of date and time (gt –
greater than).
dateHour__lt query yes/no (^) string (32) This parameter allows to
compare contribution’s
values of date and time (lt –
less than).
dateHour__gte query yes/no (^) string (32) This parameter allows to
compare contribution’s
values of date and time (gte

- greater than or equal).

dateHour__lte query yes/no (^) string (32) This parameter allows to
compare contribution’s
values of date and time (lte –
less than or equal).
playerCode query no string (32) Player’s code of a player that
is fetched.
playerCode__in query no (^) string ( 32 ) Player’s code to search for,
separated by commas.
gameCode query no (^) string (32) Game’s code of a game that
is fetched.
gameCode__in query no (^) string (64) Game’s code to search for,
separated by commas.
currency query no (^) string (3) Player’s currency.
currency__in query no (^) string (32) Currency to search for,
separated by commas.
**Note:** The option‘yes/no’ means that the operator needs to choose one of them.
The System responds to such requests according to the following schema:
**Response**
Code Schema Example
200 [^
{
dateHour: string (32),
playerCode: string (32),
gameCode: string (32),
currency: string (3),
jackpotId: string (32),
pool: string (255),
seedAmount: number,
progressiveAmount: number,

##### [

##### {

```
"dateHour": "2018- 01 -
03T17:00:00.000Z",
"playerCode":
"PL10032MOD01T2GROUP03",
"gameCode": "pt-allad-reel-
jp",
"currency": "CNY",
"jackpotId": "OMQ-JP-TWO",
```

Copyright © 2025 Skywind Holdings Ltd.

```
totalBetAmount: number,
jpWinAmount: number,
jpCurrency: string (32),
seedAmountJpCurrency: number,
progressiveAmountJpCurrency:
number,
totalBetAmountJpCurrency:
number,
jpWinAmountJpCurrency:
number,
totalBetCount: integer,
jpWinCount: integer,
firstActivity: string (32),
lastActivity: string (32),
seedWin: number,
progressiveWin: number
}
]
```
```
"pool": "Middle-amount",
"seedAmount": 12.5,
"progressiveAmount": 6.5,
"totalBetAmount": 6.5,
"jpWinAmount": 15146.6,
"jpCurrency": "EUR",
"seedAmountJpCurrency": 12.5,
```
```
"progressiveAmountJpCurrency":
6.5,
"totalBetAmountJpCurrency":
6.5,
"jpWinAmountJpCurrency":
15146.6,
"totalBetCount": 120,
"jpWinCount": 2,
"firstActivity": "2018- 01 -
03T17:10:01.020Z",
"lastActivity": "2018- 01 -
03T17:10:29.854Z",
"seedWin": 0,
"progressiveWin": 0
}
]
```
If any issues occur while getting the report, the System responds with the following

error codes:

```
HTTP Code Error Code Description
```
##### 400

##### 62

```
One of the parents is
suspended
101 Not a brand
```
##### 401

```
10 Access Token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
##### 403 206

```
User doesn’t have permission to
execute the operation
```
(^404 51) Could not find entity


Copyright © 2025 Skywind Holdings Ltd.

#### 3. Get Jackpot Contribution Logs..........................................................................................

**Request:** GET api.reportapi.com/report/jackpot/contributions/logs HTTP/1.1

This method enables the user to return a list of jackpot contribution logs which are

sorted by trxDate.

To do this, the following information should be entered according to the schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
```
offset query no
```
```
integer Number of items to be
skipped from the
beginning of the
selection.
```
```
limit query no integer
Number of items to
return (default – 20).
format query no string | “csv”
Response’s format
(CSV).
```
```
trxDate_gt query yes string (32)
```
```
This parameter allows to
compare contribution’s
values of trxDate (gt –
greater than).
```
```
trxDate_lt query yes string (32)
```
```
This parameter allows to
compare contribution’s
values of trxDate (lt –
less than).
```
The System responds to such requests according to the following schema:

**Response**

```
Code Schema Example
200 [
{
gameCode: string (32),
gameId: string (32),
roundId: string (32),
seedAmount: number,
progressiveAmount: number,
currency: string (3),
currencyRate: number,
playerCode: string (32),
insertedAt: string (32),
pool: string (255),
betAmount: number,
externalId: number
trxDate: string (32),
jackpotId: string (32)
}
]
```
##### [

##### {

```
"gameCode": "pt-allad-reel-
jp",
"gameId": "pt-allad-reel-jp",
"roundId": "g6qQOB9X",
"seedAmount": 12.5,
"progressiveAmount": 6.5,
"currency": "USD",
"currencyRate": 0.92123,
"playerCode": test,
"insertedAt": "2018- 01 -
03T17:00:00.000Z",
"pool": "mega",
"betAmount": 6.5,
"externalId": "123",
"trxDate": "2018- 01 -
03T17:00:00.000Z",
"jackpotId": "FIRE-reel"
}
]
```

Copyright © 2025 Skywind Holdings Ltd.

If any issues occur while returning the list of jackpot contribution logs, the System

responds with the following error codes:

```
HTTP Code Error Code Description
```
##### 400

```
40 Validation error
```
```
62
One of the parents is
suspended
```
```
101 Not a brand
```
##### 401

```
10 Access token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
##### 403 206

```
User doesn’t have permission to
execute the operation
```
#### 4. Get Jackpot Wins Logs

**Request:** GET api.reportapi.com/report/jackpot/contributions/wins HTTP/1.1

This method enables the user to return a list of jackpot wins logs which are sorted by

trxDate.

To do this, the following information should be entered according to the schema:

**Parameters to enter**

```
Name Located In Required Schema Notes
```
```
offset query no
```
```
integer Number of items to be
skipped from the
beginning of the
selection.
```
```
limit query no integer
Number of items to
return (default – 20).
format query no string | “csv”
Response’s format
(CSV).
```
```
trxDate_gt query yes string (32)
```
```
This parameter allows to
compare contribution’s
values of trxDate (gt –
greater than).
```
```
trxDate_lt query yes string (32)
```
```
This parameter allows to
compare contribution’s
values of trxDate (lt –
less than).
```

Copyright © 2025 Skywind Holdings Ltd.

The System responds to such requests according to the following schema:

**Response**

```
Code Schema Example
```
```
200 [^
{
gameCode: string (32),
gameId: string (32),
roundId: string (32),
seedAmount: number,
progressiveAmount: number,
currency: string (3),
currencyRate: number,
playerCode: string (32),
trxDate: string (32),
jackpotId: string (32)
insertedAt: string (32),
pool: string (255),
initialSeedAmount: 100,
externalId: number,
eventId: number,
totalSeedAmount: number,
totalProgressiveAmount: number,
winAmount: number,
winAmountCurrency: number,
trxId: number
}
]
```
##### [

##### {

```
"gameCode": "pt-allad-reel-
jp",
"gameId": "pt-allad-reel-jp",
"roundId": "ni9Gav0",
"seedAmount": 12.5,
"progressiveAmount": 6.5,
"currency": "USD",
"currencyRate": 0.92123,
"playerCode": "test",
"trxDate": "2018- 01 -
03T17:00:00.000Z",
"jackpotId": "FIRE-reel",
"pool": "mega",
"initialSeedAmount": 100,
"externalId":
"enOQKQAJew8AAALDenOQKSmDW10",
"eventID": 1,
"totalSeedAmount": 12.5,
"totalProgressiveAmount":
6.5,
"winAmount": 6.5,
"winAmountCurrency": 6.5,
"trxId": "123"
}
]
```
If any issues occur while returning the list of jackpot wins logs, the System responds

with the following error codes:

```
HTTP Code Error Code Description
```
##### 400

```
40 Validation error
```
##### 62

```
One of the parents is
suspended
```
```
101 Not a brand
```
##### 401

```
10 Access token is missing
```
```
204 Access token error
```
```
205 Access token has expired
```
```
792 Access Session is expired
```
##### 403 206

```
User doesn’t have permission to
execute the operation
```

Copyright © 2025 Skywind Holdings Ltd.

## APPENDIX

### Whitelist Solution

The Falcon System is using the Website’s Whitelisting to protect its content and give

access to use it only for registered sites.

To be secured, Operator’s website must use Falcon’s Website Whitelisting Service, that

will enable authorized sites to use Falcon System content. We achieve that via setting

a signed Site Cookie to a User’s client (browser). This process takes place on an

Operator’s website when the User enters it.

Falcon’s Website Whitelisting Service requires the operator to:

1. Provide the list of domains for Operator’s website and its mirrors
2. Add the JavaScript library to a page with the list of SW games, which is provided
    by Skywind


Copyright © 2025 Skywind Holdings Ltd.

## APPENDIX

#### CMA messages

This section describes the process of CMA (Competition & Markets Authority) bonus

message implementation. Messages are displayed to the player in response to money

transactions. Messages can either be intrusive (blocking gameplay until the action is

taken) or non-intrusive (not blocking gameplay and other functionality). This with a

boolean type parameter **nonIntrusive**.

To do this, the following information should be entered according to the schema:

**Parameters to enter**

```
Name Required Type Notes
```
msgType yes (^) string ( 64 )
Message type. Currently, can be
‘message’ only.
message yes string
Message to present to player.
Can be both plain string or HTML
code. Message must be
localized.
nonIntrusive no boolean
If specified and true, the
message can be shown in a non-
intrusive fashion. False by
default.
title no string
Optional title string to use for
popup when presenting CMA
message to player. If not
specified, default title is used. If
present, must be localized.
The System responds to such requests according to the following schema:
**Response**
Code Schema Example
200 {^
error_code: string (32),
balance: number,
currency_code: string ( 3 ),
messages: [
{
msgType: string ( 64 ),
message: string,
nonIntrusive: boolean,
title: string
},
{
msgType: string (64),
message: string,
nonIntrusive: boolean,
title: string
} ]
}

##### {

```
"error_code": 0,
"balance": 1295.43,
"currency_code": "EUR",
"messages": [
{
"msgType": "message",
"message": "You are now
starting to use your bonus
funds",
"nonIntrusive": false,
"title": "Bonus message"
},
{
"msgType": "message",
"message":
"<p>Congratulations</p><p><b>You
have reached your bonus wagering
threshold.</b></p><p>Your bonus
```

Copyright © 2025 Skywind Holdings Ltd.

```
funds are added to your main
balance</p>",
"nonIntrusive": true,
"title": "Bonus message"
}
]
}
```

Copyright © 2025 Skywind Holdings Ltd.

## APPENDIX

#### Getting Critical Files from Games and Platform

This section describes the process reporting critical files (of platform/games) as part

of the Italian, Spanish, Greek, Lithuanian, Swiss and Brazilian regulation requirements.

There is a set of methods which allows regenerating and getting the list of critical files

and their hashtags.

#### 1. Get List of Critical Files and their Hashes from Games

**Request:** POST api.criticalfilesapi.com/critical-files/games/info HTTP/1.1

This method enables the user to get the list of critical files and their hashes from the

game.

To do this, the following information should be entered according to the schema:

**Parameters to enter**

```
Name Required Type Notes
```
```
regulation yes string ( 16 )
Regulations supported: Italian, Spanish,
Greek, Lithuanian, Swiss, Brazilian
```
games yes (^) Array [string (32)] Array of game codes to get list for. Leave
array empty to get list for all available
games.
The System responds to such requests according to the following schema:
**Response**
Code Schema Example
200 {
games: [
{
code: string (64),
list: Array [string]
}
]
}

##### {

```
"games": [
{
"code": "sw_mrmnky",
"list": [
{
```
```
"lib/src/skywind/criticalFile1.js":
"1F1AE2BE9DD2E98D63E"
},
{
```
```
"lib/src/skywind/criticalFile2.js":
"51B4EAC98AF84251C4E"
}
]
}
]
}
```

```
Copyright © 2025 Skywind Holdings Ltd.
```
```
If any issues occur while getting the list of critical files, the System responds with the
following error codes:
```
```
HTTP Code Error Code Description
```
```
400 40 Validation error
```
#### 2. Get List of Critical Files and their Hashes from the Platform

**Request:** POST api.criticalfilesapi.com/critical-files/platform/info HTTP/1.1

```
This method enables the user to get the list of critical files and their hashes from the
platform.
```
To do this, the following information should be entered according to the schema:

**Parameters to enter**

```
Name Required Type Notes
```
```
regulation yes string ( 16 )
Regulations supported: Italian, Spanish,
Greek, Lithuanian, Swiss, Brazilian
```
The System responds to such requests according to the following schema:

**Response**

```
Code Schema Example
200 {^
modules: [
{
name: string (64),
list: Array [string]
}
]
}
```
##### {

```
"modules": [
{
"name": " module-name ",
"list": [
{
"lib/src/skywind/criticalFile1.js":
"1F1AE2BE9DD2E98D63E"
},
{
"lib/src/skywind/criticalFile2.js":
"51B4EAC98AF84251C4E"
}
]
}
]
}
```
```
If any issues occur while getting the list of critical files, the System responds with the
following error codes:
```
```
HTTP Code Error Code Description
400 40 Validation error
```

Copyright © 2025 Skywind Holdings Ltd.

#### 3. Get List of Critical Files and Their Hashes from Games for the Entity

**Request:** POST api.criticalfilesapi.com /entities/{path}/critical-files/games/info

HTTP/1.1

This method enables the user to get the list of critical files and their hashes from the

game for a specific entity.

To do this, the following information should be entered according to the schema:

**Parameters to enter**

```
Name Required Type Notes
```
```
regulation yes string ( 16 )
Regulations supported: Italian, Spanish, Greek,
Lithuanian, Swiss, Brazilian
```
```
path yes string ( 128 )
Business entity path that is fetched from the
business structure and it is unique for each entity.
```
games yes (^) Array [string
(32)]
Array of game codes to get list for. Leave array
empty to get list for all available games.
The System responds to such requests according to the following schema:
**Response**
Code Schema Example
200 {^
games: [
{
code: string (64),
list: Array [string]
}
]
}

##### {

```
"games": [
{
"code": "sw_mrmnky",
"list": [
{
```
```
"lib/src/skywind/criticalFile1.js":
"1F1AE2BE9DD2E98D63E"
},
{
```
```
"lib/src/skywind/criticalFile2.js":
"51B4EAC98AF84251C4E"
}
]
}
]
}
```
If any issues occur while getting the list of critical files, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
```
400 40 Validation error
```

Copyright © 2025 Skywind Holdings Ltd.

#### 4. Get List of Critical Files and Their Hashes from the Platform for the Entity.....................

**Request:** POST api.criticalfilesapi.com /entities/{path}/critical-files/platform/info

HTTP/1.1

This method enables the user to get the list of critical files and their hashes from the

platform for a specific entity.

To do this, the following information should be entered according to the schema:

**Parameters to enter**

```
Name Required Type Notes
```
```
regulation yes string ( 16 )
Regulations supported: Italian, Spanish,
Greek, Lithuanian, Swiss, Brazilian
```
```
path yes string (128)
```
```
Business entity path that is fetched from
the business structure and it is unique for
each entity.
```
The System responds to such requests according to the following schema:

**Response**

```
Code Schema Example
```
```
200 {^
modules: [
{
name: string (64),
list: Array [string]
}
]
}
```
##### {

```
" modules ": [
{
"name": " module-name ",
"list": [
{
"lib/src/skywind/criticalFile1.js":
"1F1AE2BE9DD2E98D63E"
},
{
"lib/src/skywind/criticalFile2.js":
"51B4EAC98AF84251C4E"
}
]
}
]
}
```
If any issues occur while getting the list of critical files, the System responds with the

following error codes:

```
HTTP Code Error Code Description
```
```
400 40 Validation error
```

Copyright © 2025 Skywind Holdings Ltd.

## APPENDIX

### Retry Policy

**Note:** 'error code’ below stands for error_code field in response from operator’s

system (wallet)

Skywind seamless service employs a retry mechanism for the **failed Credit and**

**Rollback** requests sent from Skywind's wallet adapter to operator wallet.

▪ There are two requests that we retry in case of failure - **/credit and /rollback**

▪ We configure a **sleep** time between retries of failed requests, and configure

```
the 'overall allowed time for request' = initial (first) request time + time of
all retries
```
▪ Retry is made only if outcome of request is unclear: **- 1 error code** from

```
operator’s wallet, unknown error code from operator or 5xx response
code/request timeout (note, that request timeout value is usually bigger
than 'overall allowed time for request', so after timeout there will be probably no
retry)
```
▪ Sleep time is doubled after each request failure

▪ In the case of retry failure, we will store a 'broken' payment on our side and

```
return an error back to the game (the player will see a popup with an error
message). Upon refreshing a game window or opening it in a new window, a new
attempt to conduct 'broken' payment will be made and retry logic will be applied
again to a request that we send to operator.
```
**Example**

Below is the example of the flow with the following configurations:

- sleep time = 0.5 sec
- overall allowed time for request = 10 sec
1. We make the first /credit request to operator which returns - 1 error code **in 1**

**sec**

2. We wait 0.5 sec and then make a new attempt to send a credit request that fails

```
with - 1 error code in 2.5 sec. Overall request time now is 1 sec + 0.5 sec +
2.5 sec = 4 sec
```
3. We double sleep time (now 1 sec) and wait until it passes. New credit request

```
fails 3.5 secs after. Overall request time now is 4 sec + 1 sec + 3.5 sec =
8.5 sec
```
4. We double sleep time (now 2 sec) and see that 8.5 sec + 2 sec of sleep time will

```
exceed the ‘overall allowed time for request’ value of 10 sec. So we wait for
another 1.5 sec = 10 sec ('overall timeout') - 8.5 sec (current overall request
time) and make the last retry attempt.
```
5. Last credit request fails, we return an error to the game and the 'broken' payment

is saved in Skywind system.


Copyright © 2025 Skywind Holdings Ltd.

6. The player sees an error popup and refreshes the game.
7. The game reloads, finds the 'broken' payment and attempts to conduct it – the

'retry' logic is applied again in this case.

**Note 1 :** According to our policy, we do not cancel Credit and Rollback transactions

but enforce player to refresh/restart game in order to make a new attempt to

conduct the broken payment.

**Note 2:** As we expect idempotency for all our transactions, meaning multiple

identical transaction requests must not multiply the result, we require that in the

case of receiving a duplicate transaction from us, you will respond with

**error_code=1** (as stated in Error Responses section) and additionally put balance

into your response as if you were sending a response to the original request.

There are also Payment (Offline) Retransmissions and Finalization mechanisms that

can be configured in Skywind system for operators to help resolve broken payments

and close open rounds while the player is offline. For 'Offline Payments' the same

Retry logic will be applied to the requests that Skywind sends to the Operator's wallet

service.


Copyright © 2025 Skywind Holdings Ltd.

## APPENDIX

### Offline payments (Retransmissions)

The operator can ask Skywind to configure offline payment retransmission – a special

feature for retrying broken payments when a player is offline.

The operator can ask Skywind to configure the following parameters in the operator

settings in the Back Office:

- ' **minPaymentRetryTimeout'** – a minimum time period in minutes after
    which the system treats the player as absent/offline.
- **‘maxPaymentRetryAttempts'** – the maximum number of attempts that the
    system will make to try to restore the interrupted game session.

The first retry attempt is made after **minPaymentRetryTimeout** is reached. If the

first attempt fails, another attempt is made in 2 minutes, then 4 minutes and so on

until **maxPaymentRetryAttempts** is reached.

In case all attempts have failed, the round is kept being considered 'broken' and to

have 'pending' payment status. A new retransmission attempt will be made if the

player comes back to the game or if in Skywind Backoffice system Skywind Support

will trigger a manual 'Retry Broken Payment' operation.


Copyright © 2025 Skywind Holdings Ltd.

## Document Version History

**Version Date Author Change Description**

1.0 21.10.2016 Guy Balteriski Initial Document Created

1.1 17.11.2016 Guy Balteriski IPM changes.

```
1.2 13.06.2017 Svetlana
Avlasenko,
```
```
Stepan
Shklyanko
```
```
The methods’ description was
restructured.
```
```
1.3 28.06.2017 Svetlana
Avlasenko,
```
```
Stepan
Shklyanko
```
```
Responses and its examples were
updated.
```
```
1.4 07.07.2017 Karina
Sidorenko
```
```
Added the game_group field to the
following methods’ responses:
```
- POST api.playerapi.com /api/validate_ticket
- POST api.playerapi.com/api/get_player

```
1.5 12.07.2017 Karina
Sidorenko
```
Added 0 and 1 Error description

```
Added the actual_bet_amount field
to the following method’s request:
```
- PUT api.playerapi.com/api/credit

```
1.6 13.07.2017 Svetlana
Avlasenko
```
```
Updated the following request and
examples:
```
- PUT on POST request in sections 2.2,
    2.3, 2.4.
- platform=1 on platform=web
- string on string: “web” | “mobile”

```
1.7 31.07.2017 Svetlana
Avlasenko
```
```
Updated the field in Parameters
table:
```
- game_status yes on game_status no


Copyright © 2025 Skywind Holdings Ltd.

```
1.8 22.08.2017 Svetlana
Avlasenko
```
```
Added free_bet_coin and
free_bet_count fields to the
responses in Credit, Debit and
Rollback operations.
```
```
1.9 28.08.2017 George Kovrey Added section with Automated Tests
API
```
```
1.10 05.09.2017 Karina
Sidorenko
```
```
Added the actual_win_amount field
to the Transfer Out method’s
request
```
```
Added Lobby to the Game URL
Parameters
```
```
1.11 12.09.2017 Karina
Sidorenko
```
Added the Marketing Tools section

```
1.12 13.09.2017 Karina
Sidorenko
```
```
Added the Free Bets flow in Seamless
Integration diagram
```
```
1.13 26.09.2017 Svetlana
Avlasenko
```
```
Updated the field with parameters for
response on Validate Ticket.
```
```
Added an error code with its
description.
```
```
1 .14 04.10.2017 Svetlana
Avlasenko
```
```
Updated the fields with examples of
curl request.
```
```
1.15 05.10.2017 Svetlana
Avlasenko
```
```
Added the info that the methods
‘transfer-in’ and ‘transfer-out’ use the
same account for all games.
```
```
1.16 09.10.2017 Svetlana
Avlasenko
```
```
Deleted the field ‘brand_id’ in
Parameters table for the chapter
2.1.1 Get Balance without Token.
```
```
1.17 12.10.2017 Svetlana
Avlasenko
```
```
Added error_code: number to all
methods.
```
```
1.18 16.10.2017 Svetlana
Alvasenko,
```
```
Stepan
Shklyanko
```
```
Added “jp_win” to the chapters 2.3.0.
Credit Customer, 2.3.1. Credit
Customer without Token, 2.3.2.
Transfer out.
“Customer Login Constraints” chapter
was deleted.
```

Copyright © 2025 Skywind Holdings Ltd.

```
1.19 19.10.2017 Svetlana
Avlasenko
```
```
Added game_code: string to the
parameters’ table for game_balance
request.
```
```
1.20 27.10.2017 Svetlana
Avlasenko
```
```
Added IP definition to the Game URL
Parameters.
```
```
1.21 30.10.2017 Karina
Sidorenko
```
```
Added jp_win parameter to 2.3.0.
Credit Customer and 2.3.1. Credit
Customer without Token
```
```
1.22 04.11.2017 Svetlana
Avlasenko
```
```
Added APPENDIX 2 Game API
methods – Get List of Games and Get
Game Info.
```
```
1.23 09.11.2017 Svetlana
Avlasenko
```
```
Parameters’ tables for 2.2. Debit
Operations and 2.3. Credit Operations
were updated.
Added examples of limits to the
methods in APPENDIX 2.
```
```
1.24 21.11.2017 Svetlana
Avlasenko,
```
```
Stepan
Shklyanko
```
```
Added length for parameters’ string
to the following methods:
1.04. Validate Ticket, 2.1.0. Get
Balance, 2.1.1. Get Balance
without token, 2.2.0. Debit
customer, 2.2.1. Transfer in,
2.3.0. Credit Customer, 2.3.1.
Credit Customer without
Token, 2.3.2. Transfer out,
2.4.0. Rollback Customer’s Bet
Transaction, 2.4.1. Rollback
without Token, 2.4.2. Get
Balance (Rollback Bet
Operations), 2 .5. Get Player’s
Information, 3.0.0. Get Free
Bets, 3.0.1. Debit Customer
with Free Bets, 3.0.2. Credit
Customer with Free Bets, 5.
Game URL Parameters, 6.1.
Get Ticket, APPENDIX 2 (1. Get
List of Games, 2. Get Game
Info) parameters’ sections.
```
```
Added parameters’ descriptions to
APPENDIX 2 Game API chapter in the
fields Notes.
```
```
1.25 0 8.12.2017 Svetlana
Avlasenko
```
```
Added jpCurrency parameter for
APPENDIX 2 in 1. Get List of Games
and 2. 2. Get Game Info methods.
```

Copyright © 2025 Skywind Holdings Ltd.

```
1.26 09.12.2017 Svetlana
Avlasenko
```
```
Added 7. Downloadable Lobby
chapter and its methods:
7.1. User Login;
7.2. Refresh Access Token
```
```
1.27 11.12.2017 Svetlana
Avlasenko
```
```
Added 7. Downloadable Lobby
section and its methods 7.1. User
Login, 7.2. Refresh Access Token.
```
```
Added description for
${api.terminalapi.com} in 1.1.
Descriptions chapter.
```
```
1.28 17.12.2017 Svetlana
Avlasenko
```
```
Terminal API: ${api.terminalapi.com}
was changed to Lobby API:
${api.lobbyapi.com}.
```
```
1.28.1 19.12.2017 Svetlana
Avlasenko
```
Syntactic error was corrected.

```
1.29 11.01.2018 Svetlana
Avlasenko
```
```
Added APPENDIX 4 Jackpot
Contribution Report.
```
```
1.3 0 26.01.2018 Svetlana
Avlasenko
```
```
The chapter 7. Downloadable Lobby
was eliminated.
```
```
1.31 12.02.2018 Svetlana
Avlasenko
```
```
Added parameter left_amount in
2.3.2. Transfer out chapter.
```
```
1 .32 16.02.2018 Svetlana
Avlasenko
```
Added 5. Get Player Game URL and

6. Get Game URL chapters.

```
1 .33 20.02.2018 Svetlana
Avlasenko
```
```
Added string variants to choose for
game_status parameter: “settled”,
“freegame”, “bonusgame”.
```
```
1.34 28.02.2018 Svetlana
Avlasenko
```
```
Added ‘Report API’ methods:
7. Get Game History,
```
8. Get Currency Report,
9. Get Player Report.

```
1.35 01.03.2018 Svetlana
Avlasenko
```
```
Added the description for Report API.
Updated the example in method 7.
Get Game History.
Fixed requests and examples of curl
throughout the whole document.
Updated the structure of the
document.
```

Copyright © 2025 Skywind Holdings Ltd.

```
1.36 02.03.2018 Svetlana
Avlasenko
```
```
Added parameter dateHour with its
description to the methods in
APPENDIX 3 1. Get Jackpot
Contribution Report and 2. Get
Players’ Jackpot Contribution Report.
Changed Jackpot Report URL:
from
api.operatorapi.com/report/jackpot/
to api.reportapi.com/report/jackpot/
```
```
1.37 06.03.2018 Svetlana
Avlasenko
```
```
Updated chapter 3. Marketing Tools
with the information about Free Bets.
```
```
1.38 07.03.2018 Svetlana
Avlasenko
```
```
Added chapter 3.1. Simple Free Bets
in BO.
```
```
1.39 14.03.2018 Svetlana
Avlasenko
```
```
Changed Global Management System
to Falcon Integration System.
```
```
1.40 20.03.2018 Svetlana
Avlasenko
```
```
Added 9. Get List of Countries, 10.
Get List of Currencies, 11. Get List of
Languages methods.
```
```
1.41 21.03.2018 Svetlana
Avlasenko
```
```
Added API flow to chapter 5. Falcon
API and the document was
restructured.
```
```
1 .42 22.03.2018 Svetlana
Avlasenko
```
```
Added the error ‘- 6 – Invalid Free Bet’
to chapter 5. Error Responses
```
```
1.43 27.03.2018 Svetlana
Avlasenko
```
```
Updated Jackpot Contribution Reports
with jpCurrency in responses.
```
```
1 .44 11 .04.2018 Svetlana
Avlasenko
```
```
Added APPENDIX 4 ‘Whitelist
Solution’;
Edited a typo in the parameter
136 irsts.
```
```
1.45 19 .04.2018 Svetlana
Avlasenko
```
The whole document was updated.

```
1.46 20.04.2018 Svetlana
Avlasenko
```
```
Added the methods Get Jackpot
Contribution Logs and Get Jackpot
Wins Logs to the APPENDIX 3
Jackpot Contribution Reports.
```

Copyright © 2025 Skywind Holdings Ltd.

```
1.47 24 .04.2018 Svetlana
Avlasenko
```
```
Added note about player’s code that
the length of it should be at least 6
symbols and may consist of
characters from a-z, A-Z, 0-9,
including the _ (underscore) and –
(dash).
```
```
1.48 02.05.2018 Svetlana
Avlasenko
```
```
Added roundId parameter to the
response for methods 3. Get Jackpot
Contribution Logs and 4. Get Jackpot
Wins Logs in APPENDIX 4.
```
```
1 .49 14.05.2018 Svetlana
Avlasenko
```
```
Updated the response in 5.8. Get
Game History method.
```
```
1.50 12.06.2018 Svetlana
Avlasenko
```
Added 5.1. Multi Domain part.

```
Edited the request for the method
5.10. Get Currency Report
```
```
Updated the Whitelist Solution
chapter.
```
```
1.51 21 .06.2018 Svetlana
Avlasenko
```
The following updates were added:

- Note for game_group parameter in
    method 2.0. Validate Ticket.
- ‘ts’, ‘dateHour’ parameters were
    changed on required.
- Parameter ‘game_status’ was
    changed on ‘settled’;
- Notes for method 5. 9. Get Game
History.
- In method 2.2.0. Debit Customer
the note for ‘event_type’ was
updated.
- Descriptions for ‘merch_id’ and
‘merch_pwd’ parameters were
adeded.
- Added the method 5.10. Get Game
Event Details Visualisation

```
1.52 26.06.2018 Svetlana
Avlasenko
```
```
Added the method 5.1 0. Get Game
History Round Details
```
```
1.53 04.07.2018 Svetlana
Avlasenko
```
```
Added ‘seedWin’ and
‘progressiveWin’ fields to the
responses in methods ‘Get Jackpot
```

Copyright © 2025 Skywind Holdings Ltd.

```
Contribution Report’ and ‘Get Player’s
Jackpot Contribution Report’
```
```
1.54 10.07.2018 Svetlana
Avlasenko
```
```
Added ‘error_msg’ to the response
for method 2 .3.0. Credit Customer.
Updated the description for the
parameter ‘timestamp’;
Added a new note to the methods
2 .1.0. Get Balance, 2.2.0. Debit
Customer, 2.3.0. Credit Customer,
2.4.0. Rollback Customer’s Bet
Transaction, 2.4.1. Rollback without
Token
```
```
3 .7.0 13.07.2018 Svetlana
Avlasenko
```
```
Added new errors to the section
‘Errors Responses’
```
```
3 .7.1 02.08.2018 Svetlana
Avlasenko
```
```
Updated the required field for ‘ts’,
‘firstTs’ , ‘paymentDate’,
‘paymentDateHour’ to ‘yes/no’ in
methods 5.9. Get Game History,
5.13. Get Player Report
```
```
3.7.2 02.08.2018 Svetlana
Avlasenko
```
```
‘Marketing Tools’ section was
removed
```
3. 9 07 .08.2018 Svetlana
    Avlasenko

Edited all the inconsistencies.

```
3.10 01.10.2018 Svetlana
Avlasenko
```
```
Added ts__gte and ts__lte
parameters to method 4.9. Get Game
History
```
```
3.10.1 08.10.2018 Svetlana
Avlasenko
```
```
Fixed the typos in the endpoints
{reportsApiURL}/v1/report/player,
{reportsApiURL}/v1/history. The ending
‘s’ has been added to the player
{reportsApiURL}/v1/report/players, and
the ‘game’ ending has been added to
{reportsApiURL}/v1/history/game.
```
```
4 .1.5 04.1 2 .2018 Svetlana
Avlasenko
```
```
Added the parameters: isGRCGame,
jackpotTypes, transferEnabled,
isBonusCoinsSupported,
isFreebetSupported to method 4. Get
List of Games
Added the note about regulatory
errors to section 3. Error responses.
```
```
4.2.4 18 .01.2019 Svetlana
Avlasenko
```
Added the following methods:


Copyright © 2025 Skywind Holdings Ltd.

```
4.9.0. Get Game History from 3rd
Party Game Providers.
4.10.0. Get Game History Details
from 3 rd Party Game Providers
4.5.0. Get Tickers for all Jackpots
```
```
4.2.4.1 29.01.2019 Svetlana
Avlasenko
```
```
Added the value “bonus_coin” to the
parameter event_type for the methods
2.3.0. Credit Customer, 2.3.1. Credit
Customer without Token
```
```
4.2.4.2 08.02.2019 Svetlana
Avlasenko
```
```
Removed ‘ts’ parameter from the
method 4.9.0. Get Game History
from 3 rd Party Game Providers
```
```
4.3 18 .02.2019 Svetlana
Avlasenko
```
```
Added the following parameters:
totalJpWin and totalContribution to
the methods 4.9. Get Game History,
4.10. Get Game History Round
Details, 2.5. Resolve round
```
```
******* 25.02.2019 Svetlana
Avlasenko
```
```
Updated the table with the - 7‘Round
not found’ error message
```
```
*******.1 01 .03.2019 Svetlana
Avlasenko
```
```
Added notes about BNS redemption
to the 4.10. Get Game History Round
Details method
```
```
*******.2 04.03.2019 Svetlana
Avlasenko
```
```
Added response schema and
example to the 2.5. Resolve Round
method
```
```
4.3.4. 14.03.2019 Svetlana
Avlasenko
```
```
Updated the method 4.5.0. Get
Tickers of All Jackpots
```
```
4.3.5. 18.03.2019 Svetlana
Avlasenko
```
```
Added the description to the 2 .5.
Resolve Round method about ‘Revert’
and ‘Close’ types
```
```
4 .4. 02 .0 4 .2019 Svetlana
Avlasenko
```
```
Added the methods 4.11. Attempts
to Finish a Problematic Round and
4.12. Attempts to Revert a
Problematic Round
Added the ‘roundId’ parameter to the
following methods:
2.2.0. Debit Customer
2.2.1. Transfer in
2.3.0. Credit Customer
2.3.2. Transfer out
```
```
4. 5. 4 11 .04.2019 Svetlana
Avlasenko
```
```
Added the ‘ip’ parameter to method
4.8. Get Game URL
```

Copyright © 2025 Skywind Holdings Ltd.

```
Added the ‘providerSpecificData’
parameter to the method ‘Get Game
History from 3 rd Party Game
Providers’
```
```
4.6.2 18.04.2019 Svetlana
Avlasenko
```
```
The parameter ‘roundId’ has been
changed on ‘round_id’ in the Debit,
Credit methods
```
```
4.8 22 .0 5 .2019 Viktoria
Bukhtarevich
```
```
Added the parameters ‘credit’ and
‘debit’ to the responses in methods
4.9. Get Game History, 4.10. Get
Game History Round Details
Added the link for Error Response
details to method 2.5. Resolve Round
Added the error_code to the 2.5.
Resolve Round response
Updated the description for the
‘resolve_method’ parameter in
method 2.5. Resolve Round
Updated the event_type option
bonus_coins to bonus_coin
Added the description for the credit’
and ‘debit’in method 4 .9. Get Game
History
```
```
4.10.6 30.05.2019 Svetlana
Avlasenko
```
```
Added the parameter ‘sessionId’ to
method 4 .9. Get Game History
```
```
4.12 18 .06.2019 Svetlana
Avlasenko
```
```
Removed the parameter ‘sessionId’
from method 4 .9. Get Game History
Added ‘debits’, ‘credits’ to the
response of Player Report
Added ‘Recovery type’ parameter to
game history report
Fixed all the inconsistencies in error
messages.
Added and described the method
4.11. Get Game Spins History for the
Brand
Added ts__gt and ts__lt parameters
to the method 4.11. Get Game Spins
History for the Brand
Updated the method 2.7. Get Ticket
Added a note about an object to the
Basic Definitions section
```

Copyright © 2025 Skywind Holdings Ltd.

```
4.12.1 05 .07.2019 Svetlana
Avlasenko
```
```
Stepan
Shklyanko
```
```
Added the ‘jp_ids’ parameter to
methods 2.3.0. Credit Customer and
2.3.1. Credit Customer without Token
```
```
Added regulatory error codes to
section 3. Error responses
```
```
4.18 02.08.2019 Svetlana
Avlasenko
```
```
The playerCode has been added to the
response in the 4.11. Get Game Spin
History for the Brand method
```
```
4.19 02 .0 8 .2019 Svetlana
Avlasenko
```
```
Added Appendix 5 which describes
CMA Messages
Added parameter ‘messages’ to the
Debit, Credit and Get Balance
methods
```
```
4 .21 28 .08.2019 Svetlana
Avlasenko
```
```
Added the parameter round_id to the
2.3.1. Credit Customer without Token
method
```
```
Added the description of parameter
types to t4.11 Get Game Spin History
for the Brand method
```
```
Added ‘rci’ and ‘rce’ parameters and
theit description to the 2.0. Validate
Ticket method
```
4. 22 21. 11 .2019 Svetlana
    Avlasenko

```
Added the parameter round_id to the
2.5. Resolve Round method
```
```
Changed the description of the
round_id parameter
```
```
Added the parameter promo_id to
2.2.0. Debit Customer and 2.3.0.
Credit Customer methods
```
```
Updated the description of method
2.5. Resolve Round
```
```
Added the table with the description
of the filters to the 4.9. Game History
method
```
```
Added the parameter ‘bonus’ to the
‘2.2.0 Debit Customer’ and ‘2.3.0
Credit Customer’ methods
```

Copyright © 2025 Skywind Holdings Ltd.

```
4.27 27 .12.2019 Svetlana
Avlasenko
```
```
Changed/added the description for
event_type and sub_trx_type
parameters in methods ‘2.2.0 Debit
customer’ and ‘2.3.0 Credit customer’
```
```
The ‘2.2.0 Debit customer’ and ‘2.3.0
Credit customer’ methods have been
updated with the ‘tournament’
parameter
```
```
For supporting the redeem operation
in BNS mode, in the method 2.3.0.
Credit Customer:
```
```
Changed the value of the ‘event_type’
field from ‘bonus_coin’ to ‘bonus’
```
```
Added the
field sub_trx_type: ‘bonus_coin’.
(Sub-type of the promotion,
extendable (other sub-types will be
added in the future). ‘bonus_coin’ is
the bonus coin redeem operation. It
is used when the player does the
Bonus Coins redeem.)
```
```
4.28 23. 01 .20 20 Svetlana
Avlasenko
```
```
The ‘2.2.0 Debit customer’ and ‘2.3.0
Credit customer’ methods have been
updated with the ‘tournament’
parameter
```
```
Changed game_id, game_code and
cust_id parameters as mandatory in
debit, credit, rollback,
get_balance and resolve_round
requests
```
```
Added the Appendix 6 ‘Getting Critical
Files’ for Italian operators
```
```
Updated the description for error
code: - 7
```
4. 29 24.01.2020 Viktoria
    Bukhtarevich

```
Updated the descriptions of ‘credit’
parameter in the methods ‘4.9. Get
Game History’ and ‘4.11. Get Game
Spin History for the Brand’ and
‘credits’ parameter in the method
‘4.16. Get Player Report’
```

Copyright © 2025 Skywind Holdings Ltd.

4. 32 10 .0 3 .2020 Viktoria
    Buhtarevich

‘Marketing Tools’ section added

```
Removed methods 2.1.1. Get Balance
without Token; 2.3.1. Credit
Customer without Token; 2.4.1.
Rollback without Token and 2.6. Get
Player’s Information
```
```
Added the error ‘ 792 – Access Session
is expired’ to the chapters
3.1.0. Create Player
3.1.1. Create Simplified Promotion for
Adding Free Bets
3.1.2. Apply Free Bet Promotion to the
Player
5.4. Refresh Access Token
5.5. Get List of Games
5.6. Get Game Info
5.7. Get Player’s Game URL
5.8. Get Game URL
5.9. Get Game History
5.10. Get Game History Round Details
5.11. Get Game Spin History for the
Brand
5.12. Attempts to Finish a Problematic
Round
5.13. Attempts to Revert a
Problematic Round
5.14. Get Game Event Details
Visualisation
5.15. Get Currency Report
5.16. Get Player Report
5.17. Get List of Countries
5.18. Get List of Currencies
5.19. Get List of Languages
```
**Appendix 3**

1. Get Jackpot Contribution Report
2. Get Players’ Jackpot Contribution
Report
3. Get Jackpot Contribution Logs
4. Get Jackpot Wins Logs

```
Added sub_trx_type parameter value
‘shared_jp_prize’ to the methods
2.2.0. Debit Customer and 2.3.0.
Credit Customer
```

Copyright © 2025 Skywind Holdings Ltd.

```
4.33 12.03.2020 Viktoria
Buhtarevich
```
```
Removed ‘language’ parameter from
the response in method 2.0. Validate
Ticket
```
```
Added a note for terminal users to the
method 2.0. Validate Ticket
```
```
Marked round_id parameter as
required in methods 2.2.0. Debit
Customer, 2.2.1. Transfer in, 2.3.0.
Credit Customer, 2.3.1. Transfer out,
2.5. Resolve Round.
```
```
Updated descriptions for game_code,
game_id and round_id parameters in
all methods where these parameters
are used
```
```
Updated the list of errors in method
5.8. Get Game URL
```
```
4.34 20.03.2020 Viktoria
Buhtarevich
```
Removed method 2.5. Resolve Round

```
4.38 04 .06.2020 Viktoria
Buhtarevich
```
```
Changed the accessToken parameter
data type from string (1024) to
varchar in response in method 5 .3.
User Login and in request in method
5 .4. Refresh Access Token
```
```
Added the optional jp_contribution
parameter to method 2.2.0. Debit
Customer
```
```
Added information on the jackpotIds
parameter format in the request in
method 5.5.0. Get tickers of All
Jackpots
```
```
Fixed the API endpoint in method
3.1.1. Create Simplified Promotion for
Adding Free Bets
```
```
4.39 16.06.2020 Viktoria
Buhtarevich
```
```
Removed the ‘type’ parameter from
the request in method 5.7. Get
Player’s Game URL
Removed the method 3.1.0. Create
Player Request: POST
```

Copyright © 2025 Skywind Holdings Ltd.

```
api.operatorapi.com/players
HTTP/1.1
```
```
Removed duplicate methods from
3.0. Marketing Tools
```
```
4 .40 24.06.2020 Viktoria
Buhtarevich
```
```
Removed the method 5.13. Attempts
to Revert a Problematic Round
```
```
Added the Responsible Gaming errors
list and the descriptions of General
errors in the 4. Error Responses
section
```
```
Added method 5.12. Get Game
History Round Details Visualisation
```
```
4.41 21.07.2020 Viktoria
Buhtarevich
```
```
Updated description of error_code 1:
Duplicate transaction in 4. Error
Responses
```
```
4.42 20.08.2020 Viktoria
Buhtarevich
```
```
Removed the ‘info’ parameter from
the response and renamed
‘additionalProp1’ to ‘jackpot-1_1’ and
‘additionalProp2’ to ‘jackpot-1_2’ in
the response example in the method
5.5.0. Get Tickers for All Jackpots
```
```
HTTP Error Codes and Error codes
columns removed in 4. Error
Responses
```
```
4.43 26.08.2020 Stepan
Shklyanko
```
```
Added the information about 2 ways
to set up and manage Free Bets – via
FIS and on the Operator’s side – to
3.0. Free Bets
```
```
Added a note that 3.0.0. Get Free Bets
method has to be implemented only if
Free Bets are set up and managed on
the Operator’s side.
```
```
Redundant information removed from
3.0.1. Debit Customer with Free Bets
and 3.0.2. Credit Customer with Free
Bets
```
```
Updated the method description and
the request scheme in 3.0.3. Create
Simplified Promotion for Adding Free
```

Copyright © 2025 Skywind Holdings Ltd.

Bets

```
Updated the method description in
3.0.4. Apply Free Bet Promotion to the
Player
```
```
4 .45 09 .10.2020 Viktoria
Buhtarevich
```
```
Removed the optional sub_trx_type
parameter from the methods Debit
Customer and Credit Customer
```
```
Removed possible event_type
parameter value “bonus” from the
methods Debit Customer and Credit
Customer
```
```
Added method 2.3.1. Bonus Payment
(Credit a Customer With a Bonus Win)
```
```
Added aamsSessionId and
aamsParticipationCode
parameters to 5.7. Get Player’s Game
URL
```
```
Renamed 5.8. Get Game URL to 5.8.
Get Fun Mode (Anonymous) Game
URL
```
```
Updated the description of possible
game_type parameter values in Debit
Customer, Credit Customer, Transfer
In and Transfer Out.
```
```
Updated the description of possible
game_status parameter values in
Credit Customer and Transfer Out
```
```
Updated the description of jp_win
parameter in Credit Customer
```

Copyright © 2025 Skywind Holdings Ltd.

```
4 .46 27.10.2020 Viktoria
Buhtarevich
```
```
Removed method 5.13. Attempts to
Finish a Problematic Round
```
```
Added examples of images received in
responses in methods 5.12 Get Game
History Round Details Visualisation
and 5.13. Get Game Event Details
Visualisation
```
```
Added information about the required
error_msg field for the cases when
the error code ≠ 0 to 4.0 Error
Responses
```
```
Added information on the supported
API request and response formats to
1.1. Description
```
```
4.47 20 .1 1 .2020 Viktoria
Buhtarevich
```
```
The parameter cust_session_id
has been marked as required in the
method 2.4. Rollback Customer’s Bet
Transaction.
```
```
A note about the cases when the
automatic rollback request is sent for
new integrations has been added to
the method 2.2.0. Debit Customer
```
```
cust_id parameter has been added
and trx_id parameter description
has been updated in the method 2.3.1
Bonus Payment.
```
```
4.48 2 3.11.2020 Viktoria
Buhtarevich
```
```
3.1.0 Bonus Payment method has
been moved to 3.0 Marketing Tools
section
```
```
4.49 09.12.2020 Viktoria
Buhtarevich
```
```
Added information about the access
token expiration time for the BO user
to Refresh Access Token.
```
```
4.50 15.12.2020 Viktoria
Buhtarevich
```
```
A note about an alternative
authentication with merch_pwd
parameter instead of hash has been
added to 3.1.0. Bonus Payment
```
```
Hash parameter has been marked as
optional in 3.1.0 Bonus Payment
```

Copyright © 2025 Skywind Holdings Ltd.

```
curl requests have been replaced
with JSON requests in all POST, PUT
and DELETE methods.
```
```
Added an optional merch_pwd
parameter to 3.1.0 Bonus Payment
```
```
4.51 29.12.2020 Viktoria
Buhtarevich
```
```
Added a note to Credit Customer and
Rollback (Refund) Customer’s Bet
Transaction about the use of the retry
flow in the case of an unsuccessful
request.
```
```
Rollback (Refund) Customer’s Bet
Transaction has been updated with
the description of the cases when this
request is sent.
```
```
Appendix 7 Retry Policy has been
added.
```
```
Appendix 8 Offline Payments
(Retransmissions) has been added.
```
```
4.52 25.01.2021 Viktoria
Buhtarevich
```
```
trx_id parameter has been added to
the response schema in 3.1.0. Bonus
Payment
```
```
4.54 18.0 3 .2021 Viktoria
Buhtarevich
```
Added method Player Logout

```
Added domains, JSON request
example and the description of the
response parameters in Get Jackpot
Tickers.
```
```
4.55 23.03.20 21 Viktoria
Buhtarevich
```
```
Added method Add Players to the
Promotion
```
```
4.55.3 25.03.2021 Viktoria
Buhtarevich
```
```
Added a new promo_type parameter
value – prize_win to Bonus
Payment.
```

Copyright © 2025 Skywind Holdings Ltd.

```
4.56 31.03.2021 Viktoria
Buhtarevich
```
```
Removed information about the
possibility to use Get Balance, Credit
and Rollback operations without
session_id
```
```
Updated ‘Ticket usage in Seamless
Integration’ diagram in Introduction.
```
```
Amount and event_type descriptions
have been fixed in Debit Customer
```
```
4.5 8 04 .0 5 .2021 Viktoria
Buhtarevich
```
```
Added an optional
jp_total_contribution parameter
to Transfer Out.
```
```
Added a note about how the rollback
method should work in case there are
multiple transactioons in a round to
Rollback (Refund) Customer’s Bet
Transaction.
```
```
Added a note about the error the
merchant must send in the case they
receive a duplicate transaction
request from Falcon to Transactions,
Debit Customer, Credit Customer,
Rollback (Refund) Customer’s Bet
Transaction and Retry Policy.
```
```
Updated the description of the
‘Duplicate Transaction’ error in Error
Responses.
```
```
Updated the error number the
merchant must send in the case the
calculated hash code and the hash
value from the request don’t match in
Bonus Payment
```
```
Updated the description of the
promo_type parameter and marked it
as optional in Bonus Payment.
```
```
4.59 11.05.2021 Viktoria
Buhtarevich
```
```
Updated the description of
‘settled’ game_status parameter
value in Credit Customer and Transfer
Out.
```

Copyright © 2025 Skywind Holdings Ltd.

```
Updated the description of the
trx_id parameter in Debit Customer,
Credit Customer, Transfer In and
Transfer Out.
```
```
Added a note on the returned
information in Get Player’s Game URL.
```
```
Added a description about the
difference between Free Bets and
Free Games to Free Bets.
```
```
Added the description of
totalFreebetWins response
parameter in Get Player Report.
```
```
Added information that the round_id
value is case sensitive to in Debit
Customer, Credit Customer, Transfer
In,Transfer Out and Player Logout.
```
```
4 .60 02.06.2021 Viktoria
Buhtarevich
```
```
Added an optional parameter
actual_bet_count to the request in
Transfer Out.
```
```
Added an optional promo_pid
parameter to the methods Debit
Customer, Credit Customer and Bonus
Payment.
```
```
4.61 11.06.2021 Viktoria
Buhtarevich
```
```
Renamed Create Simplified Promotion
for Adding Free Bets to Create a Free
Bets Promotion.
```
```
Updated the endpoint, the method
and the request example in Create a
Free Bets Promotion.
```
```
Added a note about the externalId
value being sent in the promo_id
field if it was specified during the
creation of the Bonus Coins promotion
to Bonus Payment.
```
```
4.70 19 .10.2021 Viktoria
Buhtarevich
```
```
Removed the deprecated method
Apply Free Bet Promotion to the
Player PUT
api.operatorapi.com/players/{playerC
ode}/freebet/{promoId} HTTP/1.1
```

Copyright © 2025 Skywind Holdings Ltd.

```
Added a new section Promotions with
the following methods:
```
- Create a Promotion
- Add Promo Rewards for Player
- Update a Promotion (Only for
    Bonus Coins Promotions)
- Add Players to the Promotion
- Get Promotion by ID
- Get a List of Promotions
- Delete Player From the
    Promotion
- Archive a Promotion.

```
Added a note that Free Bets
promotions can be set up in Unified
Back Office system.
```
```
Added a note about the format of
date/time parameters in Falcon API.
```
```
Added method Refresh Session
Token.
```
```
Added a note about the possibility to
implement Refresh Session Token
method by the merchants that
require unique tokens for different
games to Validate Ticket.
```
```
Added an optional request parameter
platform to Get Balance.
```
```
4.71 02.11.2021 Viktoria
Buhtarevich
```
```
Updated response example and
schema in Get Jackpot Tickers.
```
```
Removed all references to Bonus
Coins tool.
```
```
4.74 14.12.2021 Stepan
Shklyanko
```
```
Viktoria
Buhtarevich
```
Added chapter Round Finalization.

```
Updated chapter Transfer Methods –
added information on the type of
games where these methods are
used.
```

Copyright © 2025 Skywind Holdings Ltd.

```
4.75 11.01.2022 Yevheniia
Liashenko
```
Updated Error Codes in User Login.

```
4.76 18.01.2022 Yevheniia
Liashenko
```
```
Added an optional sm_result request
parameter to Credit Customer and
Transfer Out per the Portuguese
regulation requirements.
```
```
4.77 01.02.2022 Yevheniia
Liashenko
```
```
Updated request example and schema
in Create a Promotion.
```
```
4.78 22.02.2022 Yevheniia
Liashenko
```
```
Added a required operation_ts
request parameter to Debit Customer,
Transfer in, Credit Customer, Transfer
out, Rollback (Refund) Customer`s
Bet Transaction, Debit Customer with
Free Bets and Credit Customer with
Free Bets.
```
```
Added requirements for Get Ticket
method implementation.
```
```
4.79 09.03.2022 Yevheniia
Liashenko
```
```
Removed an optional info request
parameter; added required amount,
optional expirationPeriod and
expirationPeriodType request
parameters in Add Promo Rewards for
Player.
```
```
4.80 22.03.2022 Yevheniia
Liashenko
```
```
Added a note about /debit request
and 0 - bet in Offline Payment.
```
```
Added Example of JSON Request and
Response in Force-Finish.
```
```
4.81 05.04.2022 Yevheniia
Liashenko
```
```
Added more detailed description of
Refresh Session Token method.
```
```
Added a note about bet amount
sending, free_bet_coin value to
Example of JSON Request in Debit
Customer with Free Bets.
```

Copyright © 2025 Skywind Holdings Ltd.

```
4.82 19.04.2022 Yevheniia
Liashenko
```
```
Removed Transfer Methods, Transfer
in, Transfer out parts and moved
them to the ‘Skywind Arcade
Games Transfer Methods’
document.
```
```
Transfer operations and action games
were marked as deprecated in the
Response note in Get Game History,
Get Game Spin History for the Brand
and Get Player Report.
```
```
Removed ‘Action games events’ types
of parameters in Get Game Spin
History for the Brand.
```
```
Added more detailed description for
the timestamp request parameter in
Debit Customer and Credit Customer.
```
```
Added a note about 5xx response in
Appendix 7.
```
```
4.84 18 .05.2022 Yevheniia
Liashenko
```
```
Updated a description for cust_id
request parameter in Get Balance,
Debit Customer, Credit Customer,
Rollback (Refund) Customer’s Bet
Transaction, Get Free Bets and
Bonus Payment (Credit a Customer
with a Bonus Win).
```
```
Added a required round_id request
parameter in Rollback (Refund)
Customer’s Bet Transaction.
```
Updated endpoints in APPENDIX 2.

```
4.8 6 14 .0 6 .2022 Yevheniia
Liashenko
```
```
Added more detailes about rci and
rce response parameters to the
note in Validate Ticket.
```
Updated the note in Error Responses.

```
Updated the response schema and
example in Get List of Games.
```
```
Added an optional
addAggregatedFinalLimits and
currency request parameters;
```

Copyright © 2025 Skywind Holdings Ltd.

```
updated the response schema and
example in Get Game Info.
```
```
Added an optional
includeSubBrands request
parameter; added a note about
ts__gte and ts__lte request
parameters possible date range in
Get Currency Report.
```
```
Added a note about
paymentDateHour_gte and
paymentDateHour_lte request
parameters possible date range in
Get Player Report.
```
Added the Get Jackpots method.

Updated endpoints in APPENDIX 2.

```
4.87 28.06.2022 Yevheniia
Liashenko
```
```
Added an optional
distribution_type request
parameter in Debit Customer, Credit
Customer and Bonus Payment (Credit
a Customer with a Bonus Win).
```
```
Added a note about the optionality of
merch_pwd parameter in Bonus
Payment (Credit a Customer with a
Bonus Win).
```
```
Updated the introduction about HTTP
status 200 in Error Responses.
```
```
4.88 12 .07.2022 Yevheniia
Liashenko
```
```
Marked the ticket request
parameter as required (except for
the fun mode) in Get Player’s Game
URL.
```
```
4.89 26.07.2022 Yevheniia
Liashenko
```
```
Added a note that the amount
request parameter can have an
integer value in Debit Customer,
Credit Customer and Bonus Payment
(Credit a Customer with a Bonus
Win).
```
```
Added a note about hash calculation
in Bonus Payment (Credit a Customer
with a Bonus Win).
```

Copyright © 2025 Skywind Holdings Ltd.

```
Added the Get Game History Round
Details SmResult method.
```
```
4.90 09 .08.2022 Oleg Belkin Added an optional operation_ts
request parameter in Bonus Payment
(Credit a Customer with a Bonus
Win).
```
```
4.91 23.08.2022 Oleg Belkin Added new Responsible Gaming
errors in the 4. Error Responses
section.
```
4.92 07.09.2022 Oleg Belkin Bug fixes.

4.93 20.09.2022 Oleg Belkin Bug fixes.

```
4.94 03.10.2022 Oleg Belkin Added a note about country in 2.1.
Validate Ticket.
```
```
5.1 22 .03.2023 Simona Icleanu Formatted field names in Validate
ticket section.
```
Added note in Validate ticket.

```
Added disable_offers parameter to
Validate ticket.
```
```
5.2 07 .0 4 .2023 Simona Icleanu Renamed playerCodes parameter to
playersCodes in “Add Players to the
Promotion”.
```
```
Updated parameters in “Get Game
History from 3rd Party Game
Providers”.
```
```
Fixed typo in Json request from 2.4.2
Credit Customer.
```
5.3 20.04.2023 Simona Icleanu Bug fixes.

```
5.7 01.06.2023 Simona Icleanu Added note in “2.4.3 Rollback
(Refund) Customer’s Bet
Transaction”.
```
```
5.9 13.07.2023 Simona Icleanu Added note about rci/rce
parameter in 2.1 Validate Ticket.
```
```
5.10 18.07.2023 Simona Icleanu Added nickname parameter to 2.1
Validate Ticket.
```

Copyright © 2025 Skywind Holdings Ltd.

```
5.13 05.09.2023 Simona Icleanu Updated game_code parameter to
optional.
```
```
Added promo_external_id to 2.4.1.
Debit Customer, 2.4.2. Debit
Customer, 3.1.2. Debit Customer
with Free Bets and 3.1.3. Credit
Customer with Free Bets.
```
```
5.14 18.09.2023 Simona Icleanu Added externalId and
externalStartDate to 5.22. Get
Jackpots method.
```
```
5.15 05.10.2023 Simona Icleanu
Add new errors to 5.9. Get Player’s
Game URL9 & 5.10 Get Fun Mode
(Anonymous) Game URL.
```
```
Updated description of country field in
2.1 Validate Ticket.
```
```
5.17 23.10.2023 Simona Icleanu
Added new method 5.23.6. Get
Number of Free Bets for a Player
```
```
Updated note about game_group
parameter in 2.1 Validate Ticket.
```
```
5.18 24.10.2023 Simona Icleanu
Updated operation_ts parameter in
3.2.1. Bonus Payment (Credit a
Customer with a Bonus Win) from
“required” to “optional”.
```
```
5.20 28 .11.2023 Simona Icleanu
Added sm_result to the resolve_round
request example in 6.2 Round
Statistics.
```
```
5.21 4.12.2023 Simona Icleanu
Updated section 5.23.1. Create a
Promotion.
```
```
Updated note about IP address in 5.9.
Get Player’s Game URL and 5.10 Get
Fun Mode (Anonymous) Game URL.
```
```
Updated note about rci/rce functionality
in 2.1 Validate Ticket.
```
```
5.22 17 .01.2024 Simona Icleanu
Updated amount parameter value in
3.2.1 Bonus Payment.
```
```
Remove game_status from example in
JSON request in 2.4.1 Debit Customer.
```

Copyright © 2025 Skywind Holdings Ltd.

```
Updated 2.4.3. Rollback (Refund)
Customer’s Bet Transaction with note
about offline payment requests.
```
5.23 26.01.2023 Simona Icleanu (^) Added all supported regulation to
Critical Files description in **Appendix 6**.

5. 43 17.10.2024 Aleksey Stepanov
    Added new method **5.2 4. Change**
    **Player Nickname**

```
5.43 27.11.2024 Emanuel-Alin
Raileanu
```
```
Added new error code to section 4.
Error Responses
```
```
5.43 10.12.2024 Dmitriy Palaznik Added Swiss, Brazilian regulations in
Appendix 6.
```
```
5.45 18.12.2024 Emanuel-Alin
Raileanu
```
```
Corrected the description about
cust_session_id for section 6.2 Round
Statistic s
```
```
5.49 27.02.2025 Emanuel-Alin
Raileanu
```
```
Changed extraData to
providerSpecificOptions for section
5.23.1 Create a promotion
```
```
5.53 18.06.2025 Emanuel-Alin
Raileanu
```
```
Added game_type and game_status in
the response for section 2.4.3 Rollback
(Refund) Customer’s Bet Transaction
```


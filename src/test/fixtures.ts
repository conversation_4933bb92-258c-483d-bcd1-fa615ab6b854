import {
    BrandFinalizationType,
    FinalizeGameRequest,
    FreeBetInfoRequest,
    GameLogoutGameState,
    MerchantInfo,
    MerchantTransferRequest,
    MrchRoundResolveRequest,
    PaymentRequest,
    PlayMode, RefundBetRequest
} from "@skywind-group/sw-wallet-adapter-core";
import {
    SeamlessAuthTokenData,
    SeamlessGameInitRequest,
    SeamlessStartGameTokenData
} from "../skywind/entities/seamless";
import { RoundStatistics } from "@skywind-group/sw-wallet-adapter-core/lib/skywind/definitions/brokenGame";

export class TestFixtures {

    public merchantInfo: MerchantInfo = {
        type: "seamless",
        code: "seamless",
        params: {
            password: "seamlessPWD",
            serverUrl: "http://seamless.com",
            refundBetInsteadOfRetry: true,
            gameLogoutOptions: {}
        },
        brandId: 1
    };

    public merchantInfoNoZeroBet: MerchantInfo = {
        type: "seamless",
        code: "seamless",
        params: {
            password: "seamlessPWD",
            serverUrl: "http://seamless.com",
            refundBetInsteadOfRetry: true,
            gameLogoutOptions: {},
            dontSendZeroPayments: true
        },
        brandId: 1
    };

    public merchantInfoWithIgnoredRealityCheck: MerchantInfo = {
        ...this.merchantInfo,
        params: { ...this.merchantInfo.params, ignoreRealityCheck: true }
    };

    public merchantInfoWithSSLRequired: MerchantInfo = {
        ...this.merchantInfo,
        params: { ...this.merchantInfo.params, certSettings: { useCert: true } as any }
    };

    public merchantInfoWithNumericTrxId: MerchantInfo = {
        type: "seamless",
        code: "seamless",
        params: {
            password: "seamlessPWD",
            serverUrl: "http://seamless.com",
            refundBetInsteadOfRetry: true,
            gameLogoutOptions: {},
            sendNumericTrxId: true
        },
        brandId: 1
    };

    public merchantIPMInfo: MerchantInfo = {
        type: "seamless_ipm",
        code: "seamless_code_ipm",
        params: {
            password: "seamlessPWD",
            serverUrl: "http://seamless.com"
        },
        brandId: 1
    };

    public merchantInfoWithZeroPaymentsFlag: MerchantInfo = {
        type: "seamless",
        code: "seamless",
        params: {
            password: "seamlessPWD",
            serverUrl: "http://seamless.com",
            dontSendZeroPayments: true
        },
        brandId: 1
    };

    public merchantInfoWithShortCurrency: MerchantInfo = {
        type: "seamless",
        code: "seamless",
        params: {
            password: "seamlessPWD",
            serverUrl: "http://seamless.com",
            shortCurrencyEnabled: true,
        },
        brandId: 1
    };

    public providerCode = "skywind";
    public provideGameCode = "sw_mrmnky";
    public newProviderGameCode = "sw_mrmnky_new";
    public newGameCode = "sw_mrmnky_new";

    public initRequest: SeamlessGameInitRequest = {
        merchantType: "seamless",
        merchantCode: "seamless",
        gameCode: "sw_mrmnky",
        playmode: PlayMode.FUN,
        language: "en",
        ip: "127.0.0.1",
        lobby: "http://lobby.com",
        cashier: "http://cashier.com",
        ticket: "2345678",
        merch_login_url: "http://merch_login_url.com"
    };

    public initRequestFunMode: SeamlessGameInitRequest = {
        merchantType: "seamless",
        merchantCode: "seamless",
        gameCode: "sw_mrmnky",
        playmode: PlayMode.FUN,
        language: "en",
        ip: "127.0.0.1",
        lobby: "http://lobby.com",
        cashier: "http://cashier.com",
        merch_login_url: "http://merch_login_url.com"
    };

    public startGameTokenData: SeamlessStartGameTokenData = {
        country: undefined,
        currency: "USD",
        playerCode: "chebureck",
        language: "en",
        brandId: 1,
        playmode: PlayMode.REAL,
        merchantType: "seamless",
        merchantCode: "seamless",
        providerGameCode: this.provideGameCode,
        providerCode: this.providerCode,
        gameCode: this.provideGameCode,
        ipmSessionId: "test",
        test: false,
        siteUrl: "http://lobby.com"
    };

    public tokenData: SeamlessAuthTokenData = {
        ipmSessionId: "test",
        currency: "USD",
        isPromoInternal: false,
        playerCode: "chebureck",
        gameCode: "sw_mrmnky",
        brandId: 1,
        merchantType: "seamless",
        merchantCode: "seamless"
    };

    public tokenDataLive: SeamlessAuthTokenData = {
        ipmSessionId: "test",
        currency: "USD",
        isPromoInternal: false,
        playerCode: "chebureck",
        gameCode: "sw_ro_jw",
        brandId: 1,
        merchantType: "seamless",
        merchantCode: "seamless",
        isLiveGame: true
    };

    public resolveRoundRequest: MrchRoundResolveRequest = {
        gameCode: "sw_al",
        playerCode: "PL001",
        roundResolveMethod: "close",
        roundId: "1",
        roundPID: "qwertyuio"
    };

    public tokenDataIDS: SeamlessAuthTokenData = {
        ipmSessionId: "test",
        currency: "IDS",
        isPromoInternal: false,
        playerCode: "chebureck",
        gameCode: "sw_mrmnky",
        brandId: 1,
        merchantType: "seamless",
        merchantCode: "seamless"
    };

    public tokenDataVNS: SeamlessAuthTokenData = {
        ipmSessionId: "test",
        currency: "VNS",
        isPromoInternal: false,
        playerCode: "chebureck",
        gameCode: "sw_mrmnky",
        brandId: 1,
        merchantType: "seamless",
        merchantCode: "seamless"
    };

    public transferInRequest: MerchantTransferRequest = {
        operation: "transfer-in",
        gameToken: "string",
        transactionId:  { publicId: "123456" , timestamp: 1, serialId: 3232},
        roundId: "1000",
        gameSessionId: "1",
        amount: 50,
        ts: new Date().toISOString(),
    };

    public transferOutRequest: MerchantTransferRequest = {
        operation: "transfer-out",
        gameToken: "string",
        transactionId: { publicId: "123456" , timestamp: 1, serialId: 22} ,
        roundId: "1000",
        gameSessionId: "1",
        amount: 50,
        previousAmount: 55,
        actualBetAmount: 300,
        actualWinAmount: 200,
        ts: new Date().toISOString(),
    };

    public transferOutRequestWithContributionDetails: MerchantTransferRequest = { ...this.transferOutRequest,
        totalJpContribution: 0.3204,
        jackpotDetails: {
            "jackpotTypes": {
                // ...
            },
            "jackpots": {
                "SW-SUPER-LION_Solid_AFUN": {
                    "main": {
                        "contribution": {
                            "seed": 0.00004,
                            "progressive": 0.00036
                        },
                        "win": 0,
                        "isLocal": false
                    },
                    "secondary": {
                        "contribution": {
                            "seed": 0.15,
                            "progressive": 0.15
                        },
                        "win": 0,
                        "isLocal": false
                    }
                },
                "SW-SUPER-Local-JP": {
                    "main": {
                        "contribution": {
                            "seed": 0.01,
                            "progressive": 0.01
                        },
                        "win": 0,
                        "isLocal": true
                    }
                }
            }
        }
    };

    public transferOutRequestWithSmResult = { ...this.transferOutRequest,
        smResult: "0:2;3;4;2;3#2;5;5;2;3#0;7;6;4;3#"
    };

    public freebetRequest: FreeBetInfoRequest = {
        gameToken: "string",
        coinMultiplier: 100,
        stakeAll: [1, 2, 3, 4, 5]
    };

    public paymentRequest: PaymentRequest = {
        gameToken: "token",
        ts: Date.now().toString(),
        roundId: "1",
        roundPID: "roundPID",
        roundEnded: false,
        transactionId: {
            publicId: "trx",
            serialId: 1,
            timestamp: 1
        },
        bet: 100,
        win: 200
    };

    public paymentRequestWithZeroBetAndWin: PaymentRequest = {
        gameToken: "token",
        ts: Date.now().toString(),
        roundId: "1",
        roundPID: "roundPID",
        roundEnded: false,
        transactionId: {
            publicId: "trx",
            serialId: 1,
            timestamp: 1
        },
        bet: 0,
        win: 0
    };

    public refundBetRequest: RefundBetRequest = {
        gameToken: "token",
        ts: Date.now().toString(),
        roundId: "1",
        roundPID: "roundPID",
        roundEnded: false,
        transactionId: {
            publicId: "trx",
            serialId: 1,
            timestamp: 1
        },
        bet: 10,
        operation: "refund-bet"
    };

    public refundBetRequestWithZeroBet: RefundBetRequest = {
        gameToken: "token",
        ts: Date.now().toString(),
        roundId: "1",
        roundPID: "roundPID",
        roundEnded: false,
        transactionId: {
            publicId: "trx",
            serialId: 1,
            timestamp: 1
        },
        bet: 0,
        operation: "refund-bet"
    };

    public paymentRequestWithJPContribution: PaymentRequest = {
        gameToken: "token",
        ts: Date.now().toString(),
        roundId: "1",
        roundPID: "roundPID",
        roundEnded: false,
        transactionId: {
            publicId: "trx",
            serialId: 1,
            timestamp: 1
        },
        bet: 100,
        win: 200,
        totalJpContribution: 0.00199999
    };

    public paymentRequestWithJPContributionDetails: PaymentRequest = {
        gameToken: "token",
        ts: Date.now().toString(),
        roundId: "1",
        roundPID: "roundPID",
        roundEnded: false,
        transactionId: {
            publicId: "trx",
            serialId: 1,
            timestamp: 1
        },
        bet: 100,
        win: 200,
        totalJpContribution: 0.3204,
        jackpotDetails: {
            "jackpotTypes": {
                // ...
            },
            "jackpots": {
                "SW-SUPER-LION_Solid_AFUN": {
                    "main": {
                        "contribution": {
                            "seed": 0.00004,
                            "progressive": 0.00036
                        },
                        "win": 0,
                        "isLocal": false
                    },
                    "secondary": {
                        "contribution": {
                            "seed": 0.15,
                            "progressive": 0.15
                        },
                        "win": 0,
                        "isLocal": false
                    }
                },
                "SW-SUPER-Local-JP": {
                    "main": {
                        "contribution": {
                            "seed": 0.01,
                            "progressive": 0.01
                        },
                        "win": 0,
                        "isLocal": true
                    }
                }
            }
        }
    };

    public paymentRequestWithFreeBet: PaymentRequest = {
        gameToken: "token",
        ts: Date.now().toString(),
        roundId: "1",
        roundPID: "roundPID",
        roundEnded: false,
        transactionId: {
            publicId: "trx",
            serialId: 1,
            timestamp: 1
        },
        bet: 100,
        win: 200,
        freeBetCoin: 0.1
    };

    public paymentRequestWithSmResult: PaymentRequest = { ...this.paymentRequest,
        smResult: "0:2;3;4;2;3#2;5;5;2;3#0;7;6;4;3#"
    };

    public logoutPlayerRequest = {
        roundId: 1,
        roundPID: "round_pid",
        logoutId: "some_logout_id",
        gameContextId: "game_context_id",
        gameToken: "someToken",
        state: GameLogoutGameState.FINISHED
    };

    public finalizeGameRequest: FinalizeGameRequest = {
        gameToken: "token",
        roundId: "1",
        roundPID: "roundPID",
        deviceId: "WEB",
        eventId: 2,
        operation: "finalize-game",
        finalizationType: BrandFinalizationType.ROUND_STATISTICS,
        roundStatistics: {
            totalBet: 12,
            totalWin: 14,
            totalJpContribution: 0.00001,
            totalJpWin: 0
        } as RoundStatistics,
        transactionId: {
            publicId: "trx",
            serialId: 1,
            timestamp: 1
        },
        ts: Date.now().toString()
    };

    public finalizeGameRequestWithJPStats: FinalizeGameRequest = {
        ...this.finalizeGameRequest,
        roundStatistics: {
            totalWin: 50,
            totalBet: 10,
            totalJpContribution: 0.00001,
            totalEvents: 3,
            balanceBefore: 110,
            balanceAfter: 150,
            jpStatistic: {
                "SW-SUPER-LION_Solid_AFUN": {
                    "main": {
                        "contribution": {
                            "seed": 0.00004,
                            "progressive": 0.00036
                        },
                        "win": 0,
                        "isLocal": false
                    },
                    "secondary": {
                        "contribution": {
                            "seed": 0.15,
                            "progressive": 0.15
                        },
                        "win": 0,
                        "isLocal": false
                    }
                },
                "SW-SUPER-Local-JP": {
                    "main": {
                        "contribution": {
                            "seed": 0.01,
                            "progressive": 0.01
                        },
                        "win": 0,
                        "isLocal": true
                    }
                }
            }
        }
    };

    public finalizeGameRequestWithJPWinStats: FinalizeGameRequest = {
        ...this.finalizeGameRequestWithJPStats,
        roundStatistics: {
            totalWin: 50,
            totalBet: 10,
            totalJpContribution: 0.00001,
            totalJpWin: 1500,
            totalEvents: 3,
            balanceBefore: 110,
            balanceAfter: 150,
            jpStatistic: {
                "SW-SUPER-LION_Solid_AFUN": {
                    "main": {
                        "contribution": {
                            "seed": 0.00004,
                            "progressive": 0.00036
                        },
                        "win": 300,
                        "isLocal": false
                    },
                    "secondary": {
                        "contribution": {
                            "seed": 0.15,
                            "progressive": 0.15
                        },
                        "win": 300,
                        "isLocal": false
                    }
                },
                "SW-SUPER-Local-JP": {
                    "main": {
                        "contribution": {
                            "seed": 0.01,
                            "progressive": 0.01
                        },
                        "win": 900,
                        "isLocal": true
                    }
                }
            }
        }
    };

    public offlinePaymentRequest: PaymentRequest = {
        ...this.paymentRequest, finalizationType: BrandFinalizationType.OFFLINE_PAYMENTS
    };
}

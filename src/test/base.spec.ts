import { SinonSpy, spy } from "sinon";
import { should, use } from "chai";
import { BaseHttpService } from "@skywind-group/sw-wallet-adapter-core";
import { testing } from "@skywind-group/sw-utils";
import * as superAgent from "superagent";
import RequestMock = testing.RequestMock;
import * as chaiAsPromised from "chai-as-promised";
import { getSeamlessAPIService, SeamlessAPIService } from "../skywind/wallet/services/seamlessAPIService";
import { TestFixtures } from "./fixtures";
import requestMock = testing.requestMock;

export enum MOCK_ARG {
    URL,
    BODY
}

export class APIServiceSpy {
    public get: SinonSpy;
    public post: SinonSpy;
    public postMethodOfParentClass: SinonSpy;
    public put: SinonSpy;
    public delete: SinonSpy;

    constructor(service: SeamlessAPIService) {
        this.post = spy(service, "doPost");
        this.get = spy(service, "get");
        this.put = spy(service, "put");
        this.delete = spy(service, "delete");
        this.postMethodOfParentClass = spy(service, "post");
    }

    public restoreAll() {
        this.post.restore();
        this.get.restore();
        this.put.restore();
        this.delete.restore();
        this.postMethodOfParentClass.restore();
    }
}

export class BaseSpec {
    public static mocker: RequestMock;
    public merchantInfo;
    public fixtures = new TestFixtures();
    public apiService;
    public seamlessAPISpy: APIServiceSpy;

    constructor(merchantInfo) {
        this.merchantInfo = merchantInfo || this.fixtures.merchantInfo;
        this.apiService = getSeamlessAPIService(this.merchantInfo);
        this.seamlessAPISpy = new APIServiceSpy(this.apiService);
    }

    public static before() {
        should();
        use(chaiAsPromised);
        BaseSpec.mocker = requestMock(superAgent);
    }

    public after() {
        this.seamlessAPISpy.restoreAll();
        BaseSpec.mocker.clearRoutes();
    }

    public static after() {
        BaseSpec.mocker.unmock(superAgent);
    }
}

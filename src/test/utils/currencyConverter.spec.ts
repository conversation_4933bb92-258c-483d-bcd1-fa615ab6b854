import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { CurrencyConverter } from "../../skywind/utils/currencyConverter";
import { TestFixtures } from "../fixtures";
import { MerchantInfo } from "@skywind-group/sw-wallet-adapter-core";

@suite
export class CurrencyConverterSpec {
    private merchant: MerchantInfo;

    public before() {
        this.merchant = new TestFixtures().merchantInfo;

        this.merchant.params.shortCurrencyEnabled = true;
        this.merchant.params.currencies = {
            EUR: "EURO",
            USD: "DOLLAR",
            RUB: "ROUBLE_1",
            GBP: "POUND"
        };
    }

    @test
    public "fromIPMCurrency should convert currency when shortCurrencyEnabled=true"() {
        expect(CurrencyConverter.fromIPMCurrency("IDR", this.merchant)).to.equal("IDS");
        expect(CurrencyConverter.fromIPMCurrency("VND", this.merchant)).to.equal("VNS");
    }

    @test
    public "fromIPMCurrency should return original currency code when shortCurrencyEnabled=false"() {
        this.merchant.params.shortCurrencyEnabled = false;
        expect(CurrencyConverter.fromIPMCurrency("IDR", this.merchant)).to.equal("IDR");
        expect(CurrencyConverter.fromIPMCurrency("VND", this.merchant)).to.equal("VND");
    }

    @test
    public "toIPMCurrency should convert currency when shortCurrencyEnabled=true"() {
        expect(CurrencyConverter.toIPMCurrency("IDS", this.merchant)).to.equal("IDR");
        expect(CurrencyConverter.toIPMCurrency("VNS", this.merchant)).to.equal("VND");
    }

    @test
    public "toIPMCurrency should return original currency code when shortCurrencyEnabled=false"() {
        this.merchant.params.shortCurrencyEnabled = false;
        expect(CurrencyConverter.toIPMCurrency("IDS", this.merchant)).to.equal("IDS");
        expect(CurrencyConverter.toIPMCurrency("VNS", this.merchant)).to.equal("VNS");
    }

    @test
    public "fromOperatorCurrencyCode should convert currency codes correctly"() {
        expect(CurrencyConverter.fromIPMCurrency("EUR", this.merchant)).to.equal("EURO");
        expect(CurrencyConverter.fromIPMCurrency("USD", this.merchant)).to.equal("DOLLAR");
        expect(CurrencyConverter.fromIPMCurrency("GBP", this.merchant)).to.equal("POUND");
        expect(CurrencyConverter.fromIPMCurrency("XXX", this.merchant)).to.equal("XXX");
        expect(CurrencyConverter.fromIPMCurrency("RUB", this.merchant)).to.equal("ROUBLE_1");
    }

    @test
    public "toOperatorCurrencyCode should convert currency codes correctly"() {
        expect(CurrencyConverter.toIPMCurrency("EURO", this.merchant)).to.equal("EUR");
        expect(CurrencyConverter.toIPMCurrency("DOLLAR", this.merchant)).to.equal("USD");
        expect(CurrencyConverter.toIPMCurrency("POUND", this.merchant)).to.equal("GBP");
        expect(CurrencyConverter.toIPMCurrency("XXX", this.merchant)).to.equal("XXX");
        expect(CurrencyConverter.toIPMCurrency("ROUBLE_1", this.merchant)).to.equal("RUB");
    }

    @test
    public "should handle merchant without params"() {
        this.merchant.params = {};
        expect(CurrencyConverter.fromIPMCurrency("EUR", this.merchant)).to.equal("EUR");
        expect(CurrencyConverter.toIPMCurrency("EURO", this.merchant)).to.equal("EURO");
    }
}

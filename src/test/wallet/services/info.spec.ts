import { suite, test } from "mocha-typescript";
import { BaseSpec, MOCK_ARG } from "../../base.spec";
import { getInfoService, InfoService } from "../../../skywind/wallet/services/info";
import { SEAMLESS_ERROR_CODE } from "../../../skywind/errors";
import { expect } from "chai";
import { testing } from "@skywind-group/sw-utils";

import status200 = testing.status200;

@suite
class InfoSpec extends BaseSpec {
    public service: InfoService;

    public before() {
        this.service = getInfoService(this.apiService);
    }

    @test
    public async testGetFreebet() {
        BaseSpec.mocker.post(/get_free_bet/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            free_bet_count: 10,
            free_bet_coin: 0.2
        }));

        const result = await this.service.getFreeBetInfo(this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            this.fixtures.freebetRequest);

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("/api/get_free_bet");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures?.merchantInfo?.params?.password,
            cust_id: this.fixtures.tokenData.playerCode,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            game_code: this.fixtures.tokenData.gameCode,
            coin_multiplier: this.fixtures.freebetRequest.coinMultiplier,
            stake_all: this.fixtures.freebetRequest.stakeAll.join(",")
        });

        expect(result).to.be.deep.equal({
            amount: 10,
            coin: 0.2
        });
    }

    @test
    public async testPlayerInfo() {
        const playerResponse = {
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_id: "PL001",
            first_name: "qwerty",
            last_name: "uytrewq",
            email: "<EMAIL>",
            country: "US",
            language: "en",
            game_group: "vip-1",
            currency_code: "USD"
        };

        BaseSpec.mocker.post(/get_player/, status200(playerResponse));

        const result = await this.service.getPlayerInfo(this.fixtures.merchantInfo,
            this.fixtures.tokenData);

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("/api/get_player");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures?.merchantInfo?.params?.password,
            cust_id: this.fixtures.tokenData.playerCode,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
        });

        expect(result).to.be.deep.equal({
            firstName: "qwerty",
            lastName: playerResponse.last_name,
            email: playerResponse.email,
            currency: "USD",
            language: playerResponse.language,
            country: playerResponse.country,
            gameGroup: playerResponse.game_group,
            isTest: false,
            brandId: 1,
            code: "PL001",
            status: "normal"
        });
    }

    @test
    public async testPlayerInfoFor360() {
        const playerResponse = {
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_id: "PL001",
            first_name: "qwerty",
            last_name: "uytrewq",
            email: "<EMAIL>",
            country: "US",
            language: "en",
            game_group: "vip-1",
            currency_code: "USD",
            nickname: "PL001NICKNAME"
        };

        BaseSpec.mocker.post(/get_player/, status200(playerResponse));

        const result = await this.service.getPlayerInfo(this.fixtures.merchantInfo,
            this.fixtures.tokenData);

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("/api/get_player");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures?.merchantInfo?.params?.password,
            cust_id: this.fixtures.tokenData.playerCode,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
        });

        expect(result).to.be.deep.equal({
            firstName: "qwerty",
            lastName: playerResponse.last_name,
            nickname: playerResponse.nickname,
            email: playerResponse.email,
            currency: "USD",
            language: playerResponse.language,
            country: playerResponse.country,
            gameGroup: playerResponse.game_group,
            isTest: false,
            brandId: 1,
            code: "PL001",
            status: "normal"
        });
    }

    @test
    public async getPage() {
        const pageRequest = {
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            token: "aaaqa",
            lobby_id: 1,
            page_type: "playerinfo",
            language: "en",
        };

        const pageResponse = {
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            url: "http://google.com",
            size: "response.size"
        };

        BaseSpec.mocker.post(/get_page/, status200(pageResponse));

        const result = await this.service.getPage(this.fixtures.merchantInfo, {
            lobbyId: pageRequest.lobby_id,
            pageType: "playerinfo",
            language: "en",
            token: "aaaqa",
            merchantType: this.fixtures.merchantInfo.params.password,
            merchantCode: this.fixtures.merchantInfo.code
        });

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("/api/get_page");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures?.merchantInfo?.params?.password,
            token: pageRequest.token,
            lobby_id: pageRequest.lobby_id,
            page_type: pageRequest.page_type,
            language: pageRequest.language
        });

        expect(result).to.be.deep.equal({
            url: pageResponse.url,
            size: pageResponse.size
        });
    }
}

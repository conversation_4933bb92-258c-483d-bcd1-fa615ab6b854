import { suite, test } from "mocha-typescript";
import { BaseSpec } from "../../base.spec";
import { getSWError, SEAMLESS_ERROR_CODE } from "../../../skywind/errors";
import { expect } from "chai";

@suite
class InfoSpec extends BaseSpec {

    @test
    public async testGetIgnorableRealityCheck() {

        const error = getSWError(SEAMLESS_ERROR_CODE.RG_REALITY_CHECK,
            "custom message",
            this.fixtures.merchantInfoWithIgnoredRealityCheck);

        expect(error.extraData).to.exist;
        expect(error.extraData.errorVisibility).to.eq(2);
    }
}

import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { BaseSpec, MOCK_ARG } from "../../base.spec";
import { BalanceService, getBalanceService } from "../../../skywind/wallet/services/balance";
import { testing } from "@skywind-group/sw-utils";
import status200 = testing.status200;
import { SEAMLESS_ERROR_CODE } from "../../../skywind/errors";
import { SeamlessBalanceResponse } from "../../../skywind/entities/seamless";

@suite
class BalanceSpec extends BaseSpec {

    public service: BalanceService;

    public before() {
        this.service = getBalanceService(this.apiService);
    }

    @test
    public async testGetBalance() {
        BalanceSpec.mocker.post(/get_balance/, status200({
            balance: 1000,
            currency_code: "USD",
            error_code: SEAMLESS_ERROR_CODE.SUCCESS
        }));

        const result = await this.service.getBalances(this.fixtures.merchantInfo, this.fixtures.tokenData);

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/get_balance");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            cust_id: this.fixtures.tokenData.playerCode,
            game_code: this.fixtures.tokenData.gameCode,
            platform: "web"
        });

        expect(result).to.be.deep.equal({
            USD: {
                main: 1000
            }
        });
    }

    @test
    public async testGetBalanceWithFreebets() {
        BalanceSpec.mocker.post(/get_balance/, status200({
            balance: 1000,
            currency_code: "USD",
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            free_bet_count: 10
        } as SeamlessBalanceResponse));

        const result = await this.service.getBalances(this.fixtures.merchantInfo, this.fixtures.tokenData);

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(result).to.be.deep.equal({
            USD: {
                freeBets: {
                    amount: 10
                },
                main: 1000,
            }
        });
    }

    @test
    public async testGetBalanceWithInternalPromo() {
        BalanceSpec.mocker.post(/get_balance/, status200({
            balance: 1000,
            currency_code: "USD",
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            free_bet_count: 10
        } as SeamlessBalanceResponse));

        const result = await this.service.getBalances({
            ...this.fixtures.merchantInfo,
            params: {
                ...this.fixtures.merchantInfo.params,
                isPromoInternal: true
            }
        }, this.fixtures.tokenData);

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(result).to.be.deep.equal({
            USD: {
                main: 1000
            }
        });
    }

    @test
    public async testGetBalanceWithCMAMessage() {
        BalanceSpec.mocker.post(/get_balance/, status200({
            balance: 1000,
            currency_code: "USD",
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            free_bet_count: 10,
            messages: [
                {
                    msgType: "message",
                    message: "hello",
                    nonIntrusive: true,
                    title: "title non intrusive"
                },
                {
                    msgType: "message",
                    message: "hello",
                    nonIntrusive: false,
                    title: "title intrusive"
                }
            ]
        } as SeamlessBalanceResponse));

        const result = await this.service.getBalances(this.fixtures.merchantInfo, this.fixtures.tokenData);

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(result).to.be.deep.equal({
            USD: {
                extraData: {
                    messageArray: [
                        {
                            buttons: [
                                {
                                    gameAction: "continue",
                                    label: "Close",
                                    translate: true
                                }
                            ],
                            message: "hello",
                            msgTitle: "title non intrusive",
                            msgType: 1,
                            translate: false
                        },
                        {
                            buttons: [
                                {
                                    gameAction: "continue",
                                    label: "OK",
                                    translate: true
                                }
                            ],
                            message: "hello",
                            msgTitle: "title intrusive",
                            msgType: 2,
                            translate: false
                        }
                    ]
                },
                freeBets: {
                    amount: 10
                },
                main: 1000,
            }
        });
    }
}

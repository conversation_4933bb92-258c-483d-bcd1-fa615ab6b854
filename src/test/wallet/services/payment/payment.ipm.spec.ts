import { testing } from "@skywind-group/sw-utils";
import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import status200 = testing.status200;
import { BaseSpec, MOCK_ARG } from "../../../base.spec";
import { getPaymentService, PaymentService } from "../../../../skywind/wallet/services/payment";
import {
    PlayerIsSuspended,
    SEAMLESS_ERROR_CODE,
    ValidationError
} from "../../../../skywind/errors";
import { EVENT_TYPE, SeamlessBetRequest } from "../../../../skywind/entities/seamless";
import { SinonStub, stub } from "sinon";
import status400 = testing.status400;
import { TestFixtures } from "../../../fixtures";

export class BasePaymentSpec extends BaseSpec {
    public service: PaymentService;
    public serviceIPM: PaymentService;
    public getTimestampMock: SinonStub;
    public retryPolicyMock: SinonStub;

    constructor() {
        const fixtures = new TestFixtures();
        super(fixtures.merchantIPMInfo);
    }

    public before() {
        this.service = getPaymentService(this.apiService);

        this.getTimestampMock = stub(this.service as any, "getTimestamp");
        this.getTimestampMock.returns(1000000);

        this.retryPolicyMock = stub(this.service, "getRetryPolicy");
        this.retryPolicyMock.returns({
            maxTimeout: 100,
            sleep: 10,
        });
    }

    public after() {
        this.getTimestampMock.restore();
        this.retryPolicyMock.restore();
    }
}

@suite
class PaymentIPMBetSpec extends BasePaymentSpec {

    @test
    public async testCommitBetIPM() {
        BaseSpec.mocker.post(/debit/,
            status200(`error_code=${SEAMLESS_ERROR_CODE.SUCCESS}\nbalance=1000\ntrx_id=text`));

        const result = await this.service.commitBetPayment(
            this.fixtures.merchantIPMInfo,
            this.fixtures.tokenData,
            this.fixtures.paymentRequest
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantIPMInfo.code,
            merch_pwd: this.fixtures.merchantIPMInfo.params.password,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            cust_id: this.fixtures.tokenData.playerCode,
            round_id: this.fixtures.paymentRequest.roundPID,
            game_id: +this.fixtures.paymentRequest.roundId,
            game_type: "normal",
            event_id: 0,
            event_type: EVENT_TYPE.BET,
            game_code: "sw_mrmnky",
            platform: "web",
            trx_id: "trx",
            amount: 100,
            currency_code: "USD",
            timestamp: 1000000,
            operation_ts: 1
        } as SeamlessBetRequest);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1100
        });
    }

    @test
    public async testCommitBetWithFreebetIPM() {
        BaseSpec.mocker.post(/debit/, status200(
            `error_code=${SEAMLESS_ERROR_CODE.SUCCESS}\nbalance=1000\ntrx_id=text\nfree_bet_count=10`));

        const result = await this.service.commitBetPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            {
                ...this.fixtures.paymentRequest,
                freeBetCoin: 100
            }
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            cust_id: this.fixtures.tokenData.playerCode,
            round_id: this.fixtures.paymentRequest.roundPID,
            game_id: +this.fixtures.paymentRequest.roundId,
            game_type: "normal",
            event_id: 0,
            event_type: EVENT_TYPE.FREEBET,
            game_code: "sw_mrmnky",
            platform: "web",
            trx_id: "trx",
            amount: 100,
            currency_code: "USD",
            timestamp: 1000000,
            free_bet_coin: 100,
            operation_ts: 1
        } as SeamlessBetRequest);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1000,
            freeBets: {
                amount: 10
            }
        });
    }

    @test
    public async testCommitFreeSpinBetWithFreebetIPM() {
        BaseSpec.mocker.post(/debit/, status200(
            `error_code=${SEAMLESS_ERROR_CODE.SUCCESS}\nbalance=1000\ntrx_id=text\nfree_bet_count=10`));

        await this.service.commitBetPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            {
                ...this.fixtures.paymentRequest,
                freeBetMode: true
            }
        );
        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY].event_type).to.equal(EVENT_TYPE.FREEBET );
    }

    @test
    public async testBetWith200ErrorIPM() {
        BaseSpec.mocker.post(/debit/, status200(
            `error_code=${SEAMLESS_ERROR_CODE.PLAYER_IS_SUSPENDED}\nbalance=1000\ntrx_id=text`));

        await this.service.commitBetPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            this.fixtures.paymentRequest
        ).should.eventually.rejectedWith(PlayerIsSuspended);

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
    }

    @test
    public async testBetWith400ErrorShouldBeRejectedIPM() {
        BaseSpec.mocker.post(/debit/, status400({}));

        await this.service.commitBetPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            this.fixtures.paymentRequest
        ).should.eventually.rejectedWith(ValidationError);

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
    }

    @test
    public async testBetWithNullBalanceInResponseIPM() {
        BaseSpec.mocker.post(/debit/, status200(
            `error_code=${SEAMLESS_ERROR_CODE.SUCCESS}\ntrx_id=text`));

        BaseSpec.mocker.post(/get_balance/, status200(`error_code=${SEAMLESS_ERROR_CODE.SUCCESS}\nbalance=1000`));

        const result = await this.service.commitBetPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            this.fixtures.paymentRequest
        );

        expect(this.seamlessAPISpy.post.calledTwice).to.be.true;
        expect(this.seamlessAPISpy.post.firstCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/get_balance");

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1100
        });
    }

    @test
    public async testCommitWin() {
        BaseSpec.mocker.post(/credit/, status200(
            `error_code=${SEAMLESS_ERROR_CODE.SUCCESS}\ntrx_id=text\nbalance=1000`));

        this.getTimestampMock.returns(1000000);

        const result = await this.service.commitWinPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            {
                ...this.fixtures.paymentRequest,
                roundEnded: true
            }
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/credit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            cust_id: this.fixtures.tokenData.playerCode,
            round_id: this.fixtures.paymentRequest.roundPID,
            game_id: +this.fixtures.paymentRequest.roundId,
            game_type: "normal",
            event_id: 0,
            event_type: EVENT_TYPE.WIN,
            game_code: "sw_mrmnky",
            platform: "web",
            trx_id: "trx",
            amount: 200,
            currency_code: "USD",
            timestamp: 1000000,
            operation_ts: 1,
            game_status: "settled"
        } as SeamlessBetRequest);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 800
        });
    }

    @test
    public async testWinWith200ErrorShouldBeRejectedByMerchantError() {
        BaseSpec.mocker.post(/credit/, status200(
            `error_code=${SEAMLESS_ERROR_CODE.PLAYER_IS_SUSPENDED}\ntrx_id=text\nbalance=1000`));

        this.getTimestampMock.returns(1000000);

        await this.service.commitWinPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            this.fixtures.paymentRequest
        ).should.eventually.rejectedWith(PlayerIsSuspended);

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
    }

    @test
    public async testCommitFreeBetWinInternalPromo() {
        BaseSpec.mocker.post(/credit/, status200(
            `error_code=${SEAMLESS_ERROR_CODE.SUCCESS}\ntrx_id=text\nbalance=1000`));

        this.getTimestampMock.returns(1000000);

        await this.service.commitWinPayment(
            {
                ...this.fixtures.merchantInfo,
                params: {
                    ...this.fixtures.merchantInfo.params,
                    isPromoInternal: true
                }
            },
            this.fixtures.tokenData,
            {
                ...this.fixtures.paymentRequest,
                roundEnded: true,
                freeBetMode: true,
                freeBetBalance: {activePromoId: 666, amount: 13}
            }
        );

        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).deep.include({
            amount: 200,
            promo_id: "666"
        } as SeamlessBetRequest);
    }
}

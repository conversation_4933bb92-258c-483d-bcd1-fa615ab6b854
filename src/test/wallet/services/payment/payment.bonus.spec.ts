import { BasePaymentSpec } from "./payment.bet.spec";
import { BaseSpec, MOCK_ARG } from "../../../base.spec";
import { SEAMLESS_ERROR_CODE } from "../../../../skywind/errors";
import { expect } from "chai";
import {
    EVENT_TYPE,
    SeamlessBetRequest,
    SeamlessOfflineBonusRequest, SeamlessPaymentRequest
} from "../../../../skywind/entities/seamless";
import { testing } from "@skywind-group/sw-utils";
import * as crypto from "crypto";
import { suite, test } from "mocha-typescript";
import status200 = testing.status200;
import config from "../../../../skywind/config";

@suite
class PaymentBonusSpec extends BasePaymentSpec {
    @test
    public async testCommitBonusPayment() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 900,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));
        BaseSpec.mocker.post(/credit/, status200({
            balance: 1100,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));
        this.getTimestampMock.returns(1000000);

        const result = await this.service.commitBonusPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            {
                ...this.fixtures.paymentRequest,
                promoId: "egor_chebureck",
                promoType: "egor_chebureck_type",
                roundEnded: true
            }
        );

        expect(this.seamlessAPISpy.post.calledTwice).to.be.true;
        expect(this.seamlessAPISpy.post.firstCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
        expect(this.seamlessAPISpy.post.firstCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            cust_id: this.fixtures.tokenData.playerCode,
            round_id: this.fixtures.paymentRequest.roundPID,
            game_id: +this.fixtures.paymentRequest.roundId,
            game_type: "normal",
            event_id: 0,
            event_type: EVENT_TYPE.BONUS,
            game_code: "sw_mrmnky",
            platform: "web",
            trx_id: "trx",
            amount: 100,
            currency_code: "USD",
            timestamp: 1000000,
            promo_id: "egor_chebureck",
            sub_trx_type: "egor_chebureck_type",
            operation_ts: 1
        } as SeamlessBetRequest);

        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            cust_id: this.fixtures.tokenData.playerCode,
            round_id: this.fixtures.paymentRequest.roundPID,
            game_id: +this.fixtures.paymentRequest.roundId,
            game_type: "normal",
            event_id: 0,
            event_type: EVENT_TYPE.BONUS,
            game_code: "sw_mrmnky",
            platform: "web",
            trx_id: "trx",
            amount: 200,
            currency_code: "USD",
            timestamp: 1000000,
            game_status: "settled",
            promo_id: "egor_chebureck",
            sub_trx_type: "egor_chebureck_type",
            operation_ts: 1
        } as SeamlessBetRequest);

        expect(result).to.be.deep.equal({
            main: 1100,
            previousValue: 1000
        });
    }

    @test
    public async testCommitOfflineBonusPayment() {
        BaseSpec.mocker.post(/bonus/, status200({
            balance: 900,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));
        BaseSpec.mocker.get(
            `${config.internalMAPIUrl}/v1/merchantentities/` +
            `${this.fixtures.merchantInfo.type}/${this.fixtures.merchantInfo.code}/settings/`,
            status200({ merchant: this.fixtures.merchantInfo }));
        this.getTimestampMock.returns(1000000);

        const result = await this.service.commitOfflineBonusPayment(
            this.fixtures.merchantInfo,
            {
                amount: 100,
                transactionId: { serialId: 1, publicId: "trx", timestamp: Date.now() },
                playerCode: "Customer123",
                currencyCode: "USD",
                promoId: "promo-id",
                promoType: "tournament",
            }
        );

        const dataString = "amount=100&currency_code=USD&cust_id=Customer123&merch_id=" +
            this.fixtures.merchantInfo.code +
            "&promo_id=promo-id&" +
            "promo_type=tournament&" +
            "timestamp=1000000&trx_id=trx";
        const hash = crypto.createHash("md5").update(dataString + this.fixtures.merchantInfo.params.password)
            .digest("hex");

        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            trx_id: "trx",
            cust_id: "Customer123",
            amount: 100,
            currency_code: "USD",
            merch_pwd: undefined,
            timestamp: 1000000,
            promo_id: "promo-id",
            promo_type: "tournament",
            hash
        } as SeamlessOfflineBonusRequest);

        expect(result).to.be.deep.equal({
            balance: { main: 900} ,
            externalTrxId: "text"
        });
    }

    @test
    public async testCommitOfflineBonusPaymentAsPromoIdNumber() {
        BaseSpec.mocker.post(/bonus/, status200({
            balance: 900,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));
        this.getTimestampMock.returns(1000000);

        const result = await this.service.commitOfflineBonusPayment(
            this.fixtures.merchantInfo,
            {
                amount: 100,
                transactionId: { serialId: 1, publicId: "trx", timestamp: Date.now() },
                playerCode: "Customer123",
                currencyCode: "USD",
                promoId: "1",
                promoType: "tournament",
            }
        );

        const dataString = "amount=100&currency_code=USD&cust_id=Customer123&merch_id=" +
            this.fixtures.merchantInfo.code +
            "&promo_id=1&promo_pid=W4RkGRen&" +
            "promo_type=tournament&" +
            "timestamp=1000000&trx_id=trx";
        const hash = crypto.createHash("md5").update(dataString + this.fixtures.merchantInfo.params.password)
            .digest("hex");

        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            trx_id: "trx",
            cust_id: "Customer123",
            amount: 100,
            currency_code: "USD",
            merch_pwd: undefined,
            timestamp: 1000000,
            promo_id: "1",
            promo_type: "tournament",
            promo_pid: "W4RkGRen",
            hash
        } as SeamlessOfflineBonusRequest);

        expect(result).to.be.deep.equal({
            balance: { main: 900} ,
            externalTrxId: "text"
        });
    }

    @test
    public async testCommitBonusPaymentAsExternalPromoIdNumber() {
        BaseSpec.mocker.post(/credit/, status200({
            balance: 900,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));
        this.getTimestampMock.returns(1000000);

        const result = await this.service.commitWinPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            {
                ...this.fixtures.paymentRequest,
                promoId: "egor_chebureck",
                promoType: "egor_chebureck_type",
                externalId: "anyRandomId",
                roundEnded: true,
                freeBetBalance: {
                    amount: 100,
                    externalId: "anyRandomId",
                },
                freeBetMode: true
            }
        );

        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            trx_id: "trx",
            cust_id: "chebureck",
            amount: 200,
            currency_code: "USD",
            timestamp: 1000000,
            round_id: this.fixtures.paymentRequest.roundPID,
            game_id: +this.fixtures.paymentRequest.roundId,
            promo_id: "egor_chebureck",
            promo_external_id: "anyRandomId", // should be done eventyally
            merch_pwd: "seamlessPWD",
            operation_ts: 1,
            platform: "web",
            cust_session_id: "test",
            event_id: 0,
            event_type: "egor_chebureck_type",
            game_code: "sw_mrmnky",
            game_type: "normal",
            game_status: "settled"
        } as SeamlessPaymentRequest);

        expect(result).to.be.deep.equal({
            main: 900,
            previousValue: 700
        });
    }
}

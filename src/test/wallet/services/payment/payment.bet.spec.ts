import { testing } from "@skywind-group/sw-utils";
import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import status200 = testing.status200;
import { BaseSpec, MOCK_ARG } from "../../../base.spec";
import { getPaymentService, PaymentService } from "../../../../skywind/wallet/services/payment";
import {
    ExceedBetLimit,
    PlayerIsSuspended,
    SEAMLESS_ERROR_CODE,
    ValidationError
} from "../../../../skywind/errors";
import { EVENT_TYPE, SeamlessBetRequest } from "../../../../skywind/entities/seamless";
import { SinonStub, stub } from "sinon";
import status400 = testing.status400;
import { CannotCompletePayment } from "@skywind-group/sw-wallet-adapter-core";

export class BasePaymentSpec extends BaseSpec {
    public service: PaymentService;
    public getTimestampMock: SinonStub;
    public retryPolicyMock: SinonStub;

    public before() {
        this.service = getPaymentService(this.apiService);

        this.getTimestampMock = stub(this.service as any, "getTimestamp");
        this.getTimestampMock.returns(1000000);

        this.retryPolicyMock = stub(this.service, "getRetryPolicy");
        this.retryPolicyMock.returns({
            maxTimeout: 100,
            sleep: 10,
        });
    }

    public after() {
        this.getTimestampMock.restore();
        this.retryPolicyMock.restore();
    }
}

@suite
class PaymentBetSpec extends BasePaymentSpec {
    @test
    public async testCommitBet() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));

        const result = await this.service.commitBetPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            this.fixtures.paymentRequest
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            cust_id: this.fixtures.tokenData.playerCode,
            round_id: this.fixtures.paymentRequest.roundPID,
            game_id: +this.fixtures.paymentRequest.roundId,
            game_type: "normal",
            event_id: 0,
            event_type: EVENT_TYPE.BET,
            game_code: "sw_mrmnky",
            platform: "web",
            trx_id: "trx",
            amount: 100,
            currency_code: "USD",
            timestamp: 1000000,
            operation_ts: 1
        } as SeamlessBetRequest);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1100
        });
    }

    @test
    public async testCommitBetWithPromo() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));

        const result = await this.service.commitBetPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            {
                ...this.fixtures.paymentRequest,
                promoId: "1",
                promoType: "bns"
            }
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            cust_id: this.fixtures.tokenData.playerCode,
            round_id: this.fixtures.paymentRequest.roundPID,
            game_id: +this.fixtures.paymentRequest.roundId,
            game_type: "normal",
            event_id: 0,
            event_type: "bns",
            game_code: "sw_mrmnky",
            platform: "web",
            trx_id: "trx",
            amount: 100,
            currency_code: "USD",
            timestamp: 1000000,
            operation_ts: 1,
            promo_id: "1",
            promo_pid: "W4RkGRen"
        } as SeamlessBetRequest);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1100
        });
    }

    @test
    public async testCommitBetWithJPContribution() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));

        const result = await this.service.commitBetPayment(
            { ...this.fixtures.merchantInfo, params: {
                    password: "seamlessPWD",
                    serverUrl: "http://seamless.com",
                    reportJPContributionOnDebitForSeamless: true
                }},
            this.fixtures.tokenData,
            this.fixtures.paymentRequestWithJPContribution
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            cust_id: this.fixtures.tokenData.playerCode,
            round_id: this.fixtures.paymentRequest.roundPID,
            game_id: +this.fixtures.paymentRequest.roundId,
            game_type: "normal",
            event_id: 0,
            event_type: EVENT_TYPE.BET,
            game_code: "sw_mrmnky",
            platform: "web",
            trx_id: "trx",
            amount: 100,
            currency_code: "USD",
            timestamp: 1000000,
            operation_ts: 1,
            jp_contribution: 0.00199999
        } as SeamlessBetRequest);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1100
        });
    }
    @test
    public async testCommitBetWithJPContributionDetails() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));

        const result = await this.service.commitBetPayment(
            { ...this.fixtures.merchantInfo, params: {
                    password: "seamlessPWD",
                    serverUrl: "http://seamless.com",
                    reportJPContributionOnDebitForSeamless: true
                }},
            this.fixtures.tokenData,
            this.fixtures.paymentRequestWithJPContributionDetails
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            cust_id: this.fixtures.tokenData.playerCode,
            round_id: this.fixtures.paymentRequest.roundPID,
            game_id: +this.fixtures.paymentRequest.roundId,
            game_type: "normal",
            event_id: 0,
            event_type: EVENT_TYPE.BET,
            game_code: "sw_mrmnky",
            platform: "web",
            trx_id: "trx",
            amount: 100,
            currency_code: "USD",
            timestamp: 1000000,
            operation_ts: 1,
            jp_contribution: 0.3204,
            jp_contribution_details: [
                {
                    jackpot_id: "SW-SUPER-LION_Solid_AFUN",
                    jp_contribution: 0.3004,
                    pools: [
                        {
                            is_global_pool: true,
                            jp_contribution: 0.0004,
                            pool_id: "main"
                        },
                        {
                            is_global_pool: true,
                            jp_contribution: 0.3,
                            pool_id: "secondary"
                        }
                    ]
                },
                {
                    jackpot_id: "SW-SUPER-Local-JP",
                    jp_contribution: 0.02,
                    pools: [
                        {
                            is_global_pool: false,
                            jp_contribution: 0.02,
                            pool_id: "main"
                        }
                    ]
                }
            ]
        } as SeamlessBetRequest);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1100
        });
    }

    @test
    public async testCommitBetWithJPContributionDetailsWithRounding() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));

        await this.service.commitBetPayment(
            { ...this.fixtures.merchantInfo, params: {
                    password: "seamlessPWD",
                    serverUrl: "http://seamless.com",
                    reportJPContributionOnDebitForSeamless: true
                }},
            this.fixtures.tokenData,
            {
                ...this.fixtures.paymentRequestWithJPContributionDetails,
                jackpotDetails: {
                    "jackpots": {
                        "SW-THE-LAST-KINGDOM-MINI": {
                            "mini": {
                                "contribution": {
                                    "seed": 0.000290322580645,
                                    "progressive": 0.004709677419355
                                },
                                "win": 0,
                                "isLocal": false
                            }
                        },
                        "SW-THE-LAST-KINGDOM-GRAND": {
                            "grand": {
                                "contribution": {
                                    "seed": 0.00015,
                                    "progressive": 0.0023499999999999997
                                },
                                "win": 0,
                                "isLocal": false
                            }
                        }
                    },
                    "jackpotTypes": {
                        "sw-the-last-kingdom-mini": [
                            "SW-THE-LAST-KINGDOM-MINI"
                        ],
                        "sw-the-last-kingdom-grand": [
                            "SW-THE-LAST-KINGDOM-GRAND"
                        ]
                    }
                } as any},
        );

        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY].jp_contribution_details).deep.equal([
                {
                    jackpot_id: "SW-THE-LAST-KINGDOM-MINI",
                    jp_contribution: 0.005,
                    pools: [
                        {
                            is_global_pool: true,
                            jp_contribution: 0.005,
                            pool_id: "mini"
                        }
                    ]
                },
                {
                    jackpot_id: "SW-THE-LAST-KINGDOM-GRAND",
                    jp_contribution: 0.0025,
                    pools: [
                        {
                            is_global_pool: true,
                            jp_contribution: 0.0025,
                            pool_id: "grand"
                        }
                    ]
                }
            ]);
    }

    @test
    public async testCommitBetWithFreebet() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            free_bet_count: 10,
            trx_id: "text"
        }));

        const result = await this.service.commitBetPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            {
                ...this.fixtures.paymentRequest,
                freeBetCoin: 100
            }
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            cust_id: this.fixtures.tokenData.playerCode,
            round_id: this.fixtures.paymentRequest.roundPID,
            game_id: +this.fixtures.paymentRequest.roundId,
            game_type: "normal",
            event_id: 0,
            event_type: EVENT_TYPE.FREEBET,
            game_code: "sw_mrmnky",
            platform: "web",
            trx_id: "trx",
            amount: 100,
            currency_code: "USD",
            timestamp: 1000000,
            operation_ts: 1,
            free_bet_coin: 100
        } as SeamlessBetRequest);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1000,
            freeBets: {
                amount: 10
            }
        });
    }

    @test
    public async testBetWith200Error() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.PLAYER_IS_SUSPENDED,
            trx_id: "text"
        }));

        await this.service.commitBetPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            this.fixtures.paymentRequest
        ).should.eventually.rejectedWith(PlayerIsSuspended);

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
    }

    @test
    public async testBetErrorExceedBetLimitWithExtraData() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.EXCEED_BET_LIMIT,
            trx_id: "text"
        }));

        const error = await this.service.commitBetPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            this.fixtures.paymentRequest
        ).should.eventually.rejectedWith(ExceedBetLimit);
        expect(error.extraData).is.not.undefined;
        expect(error.extraData.messageArray).is.not.empty;
        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
    }

    @test  // use any error that should be threw without extra data
    public async testBetErrorPlayerIsSuspendedWithoutExtraData() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.PLAYER_IS_SUSPENDED,
            trx_id: "text"
        }));

        const error = await this.service.commitBetPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            this.fixtures.paymentRequest
        ).should.eventually.rejectedWith(PlayerIsSuspended);
        expect(error.extraData).is.undefined;
        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
    }

    @test
    public async testBetWith400ErrorShouldBeRejected() {
        BaseSpec.mocker.post(/debit/, status400({}));

        await this.service.commitBetPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            this.fixtures.paymentRequest
        ).should.eventually.rejectedWith(ValidationError);

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
    }

    @test
    public async testBetWithNullBalanceInResponse() {
        BaseSpec.mocker.post(/debit/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "test"
        }));

        BaseSpec.mocker.post(/get_balance/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            balance: 1000
        }));

        const result = await this.service.commitBetPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            this.fixtures.paymentRequest
        );

        expect(this.seamlessAPISpy.post.calledTwice).to.be.true;
        expect(this.seamlessAPISpy.post.firstCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/get_balance");

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1100
        });
    }

    @test
    public async testNumericTrxId() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));

        await this.service.commitBetPayment(
            this.fixtures.merchantInfoWithNumericTrxId,
            this.fixtures.tokenData,
            this.fixtures.paymentRequest
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            cust_id: this.fixtures.tokenData.playerCode,
            round_id: this.fixtures.paymentRequest.roundPID,
            game_id: +this.fixtures.paymentRequest.roundId,
            game_type: "normal",
            event_id: 0,
            event_type: EVENT_TYPE.BET,
            game_code: "sw_mrmnky",
            platform: "web",
            trx_id: 1,
            amount: 100,
            currency_code: "USD",
            timestamp: 1000000,
            operation_ts: 1
        } as SeamlessBetRequest);
    }

    @test
    public async testZeroPaymentFlag_ZeroBetIsNotSent() {
        BaseSpec.mocker.post(/get_balance/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS
        }));

        await this.service.commitBetPayment(
            this.fixtures.merchantInfoWithZeroPaymentsFlag,
            this.fixtures.tokenData,
            this.fixtures.paymentRequestWithZeroBetAndWin
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/get_balance");
    }

    @test
    public async testZeroPaymentFlag_ZeroBetIsSentIfItsOpeningBet() {
        BaseSpec.mocker.post(/get_balance/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS
        }));

        await this.service.commitBetPayment(
            this.fixtures.merchantInfoWithZeroPaymentsFlag,
            this.fixtures.tokenData,
            { ...this.fixtures.paymentRequestWithZeroBetAndWin, eventId: 0 }
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
    }

    @test()
    public async duplicateTrxWithExpiredToken() {
        BaseSpec.mocker.post(/debit/, status200({
            error_code: SEAMLESS_ERROR_CODE.DUPLICATE_TRX_ERROR,
            trx_id: "text"
        }));

        BaseSpec.mocker.post(/get_balance/, status200({
            error_code: SEAMLESS_ERROR_CODE.GAME_TOKEN_EXPIRED,
            trx_id: "text"
        }));

        await this.service.commitBetPayment(
            this.fixtures.merchantInfoWithNumericTrxId,
            this.fixtures.tokenData,
            this.fixtures.paymentRequest
        ).should.eventually.rejectedWith(CannotCompletePayment);
    }

    @test
    public async testCommitOfflineBet() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));

        const result = await this.service.commitBetPayment(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            this.fixtures.offlinePaymentRequest
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            cust_id: this.fixtures.tokenData.playerCode,
            round_id: this.fixtures.paymentRequest.roundPID,
            game_id: +this.fixtures.paymentRequest.roundId,
            game_type: "normal",
            event_id: 0,
            event_type: EVENT_TYPE.BET,
            game_code: "sw_mrmnky",
            platform: "web",
            trx_id: "trx",
            amount: 100,
            currency_code: "USD",
            timestamp: 1000000,
            operation_ts: 1,
            is_finalization_payment: true
        } as SeamlessBetRequest);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1100
        });
    }
}

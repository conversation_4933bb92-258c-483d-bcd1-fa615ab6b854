import { suite, test } from "mocha-typescript";
import { BasePaymentSpec } from "./payment.bet.spec";
import { BaseSpec, MOCK_ARG } from "../../../base.spec";
import { SEAMLESS_ERROR_CODE } from "../../../../skywind/errors";
import { expect } from "chai";
import { testing } from "@skywind-group/sw-utils";
import status200 = testing.status200;

@suite
class PaymentTransferSpec extends BasePaymentSpec {

    @test
    public async testTransferIn() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));
        this.getTimestampMock.returns(1000000);

        const result = await this.service.transfer(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            this.fixtures.transferInRequest
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("/api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            "amount": this.fixtures.transferInRequest.amount,
            "currency_code": "USD",
            "cust_id": this.fixtures.tokenData.playerCode,
            "cust_session_id": this.fixtures.tokenData.ipmSessionId,
            "event_id": 0,
            "event_type": "transfer-in",
            "game_status": "freegame",
            "game_code": this.fixtures.tokenData.gameCode,
            "game_id": 1000,
            "round_id": "1000",
            "merch_id": this.fixtures.merchantInfo.code,
            "merch_pwd": this.fixtures.merchantInfo.params.password,
            "trx_id": this.fixtures.transferInRequest.transactionId.publicId,
            "timestamp": 1000000,
            "game_type": "normal",
            "platform": "web",
            operation_ts: 1
        } as any);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1050
        });
    }

    @test
    public async testTransferInIDS() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));
        this.getTimestampMock.returns(1000000);

        const result = await this.service.transfer(
            this.fixtures.merchantInfoWithShortCurrency,
            this.fixtures.tokenDataIDS,
            this.fixtures.transferInRequest
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("/api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            "amount": this.fixtures.transferInRequest.amount,
            "currency_code": "IDR",
            "cust_id": this.fixtures.tokenDataIDS.playerCode,
            "cust_session_id": this.fixtures.tokenDataIDS.ipmSessionId,
            "event_id": 0,
            "event_type": "transfer-in",
            "game_status": "freegame",
            "game_code": this.fixtures.tokenDataIDS.gameCode,
            "game_id": 1000,
            "round_id": "1000",
            "merch_id": this.fixtures.merchantInfoWithShortCurrency.code,
            "merch_pwd": this.fixtures.merchantInfoWithShortCurrency.params.password,
            "trx_id": this.fixtures.transferInRequest.transactionId.publicId,
            "timestamp": 1000000,
            "game_type": "normal",
            "platform": "web",
            operation_ts: 1
        } as any);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1050
        });
    }

    @test
    public async testTransferInVNS() {
        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));
        this.getTimestampMock.returns(1000000);

        const result = await this.service.transfer(
            this.fixtures.merchantInfoWithShortCurrency,
            this.fixtures.tokenDataVNS,
            this.fixtures.transferInRequest
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("/api/debit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            "amount": this.fixtures.transferInRequest.amount,
            "currency_code": "VND",
            "cust_id": this.fixtures.tokenDataVNS.playerCode,
            "cust_session_id": this.fixtures.tokenDataVNS.ipmSessionId,
            "event_id": 0,
            "event_type": "transfer-in",
            "game_status": "freegame",
            "game_code": this.fixtures.tokenDataVNS.gameCode,
            "game_id": 1000,
            "round_id": "1000",
            "merch_id": this.fixtures.merchantInfoWithShortCurrency.code,
            "merch_pwd": this.fixtures.merchantInfoWithShortCurrency.params.password,
            "trx_id": this.fixtures.transferInRequest.transactionId.publicId,
            "timestamp": 1000000,
            "game_type": "normal",
            "platform": "web",
            operation_ts: 1
        } as any);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1050
        });
    }

    @test
    public async testTransferOut() {
        BaseSpec.mocker.post(/credit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));
        this.getTimestampMock.returns(1000000);

        const result = await this.service.transfer(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            {
                ...this.fixtures.transferOutRequest,
                betsCount: 88,
                roundEnded: true
            }
        );

        expect(this.seamlessAPISpy.post.calledTwice).to.be.true;
        expect(this.seamlessAPISpy.post.firstCall.args[MOCK_ARG.URL]).to.be.equal("/api/debit");
        expect(this.seamlessAPISpy.post.firstCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            "amount": 0,
            "currency_code": "USD",
            "cust_id": this.fixtures.tokenData.playerCode,
            "cust_session_id": this.fixtures.tokenData.ipmSessionId,
            "event_id": 0,
            "event_type": "transfer-in",
            "game_status": "freegame",
            "game_code": this.fixtures.tokenData.gameCode,
            "game_id": 1000,
            "round_id": "1000",
            "merch_id": this.fixtures.merchantInfo.code,
            "merch_pwd": this.fixtures.merchantInfo.params.password,
            "trx_id": this.fixtures.transferInRequest.transactionId.publicId,
            "timestamp": 1000000,
            "game_type": "normal",
            "platform": "web",
            operation_ts: 1
        } as any);

        expect(this.seamlessAPISpy.post.secondCall.args[MOCK_ARG.URL]).to.be.equal("/api/credit");
        expect(this.seamlessAPISpy.post.secondCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            "amount": this.fixtures.transferInRequest.amount,
            "currency_code": "USD",
            "cust_id": this.fixtures.tokenData.playerCode,
            "cust_session_id": this.fixtures.tokenData.ipmSessionId,
            "event_id": 0,
            "event_type": "transfer-out",
            "game_status": "settled",
            "game_code": this.fixtures.tokenData.gameCode,
            "game_id": 1000,
            "round_id": "1000",
            "merch_id": this.fixtures.merchantInfo.code,
            "merch_pwd": this.fixtures.merchantInfo.params.password,
            "trx_id": this.fixtures.transferInRequest.transactionId.publicId,
            "timestamp": 1000000,
            "game_type": "normal",
            "platform": "web",
            "actual_bet_amount": 300,
            "actual_win_amount": 200,
            "jp_win_amount": 0,
            "left_amount": 5,
            "jp_total_contribution": 0,
            actual_bet_count: 88,
            operation_ts: 1
        } as any);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 950
        });
    }

    @test
    public async testTransferOutWithJPContributionDetails() {
        BaseSpec.mocker.post(/credit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));
        this.getTimestampMock.returns(1000000);

        const result = await this.service.transfer(
            {
                ...this.fixtures.merchantInfo,
                params: {
                    ...this.fixtures.merchantInfo.params, reportJPContributionOnDebitForSeamless: true
                }
            },
            this.fixtures.tokenData,
            {
                ...this.fixtures.transferOutRequestWithContributionDetails,
                betsCount: 88,
                roundEnded: true
            }
        );

        expect(this.seamlessAPISpy.post.calledTwice).to.be.true;
        expect(this.seamlessAPISpy.post.firstCall.args[MOCK_ARG.URL]).to.be.equal("/api/debit");
        expect(this.seamlessAPISpy.post.firstCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            "amount": 0,
            "currency_code": "USD",
            "cust_id": this.fixtures.tokenData.playerCode,
            "cust_session_id": this.fixtures.tokenData.ipmSessionId,
            "event_id": 0,
            "event_type": "transfer-in",
            "game_status": "freegame",
            "game_code": this.fixtures.tokenData.gameCode,
            "game_id": 1000,
            "round_id": "1000",
            "merch_id": this.fixtures.merchantInfo.code,
            "merch_pwd": this.fixtures.merchantInfo.params.password,
            "trx_id": this.fixtures.transferInRequest.transactionId.publicId,
            "timestamp": 1000000,
            "game_type": "normal",
            "platform": "web",
            operation_ts: 1
        } as any);

        expect(this.seamlessAPISpy.post.secondCall.args[MOCK_ARG.URL]).to.be.equal("/api/credit");
        expect(this.seamlessAPISpy.post.secondCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            "amount": this.fixtures.transferInRequest.amount,
            "currency_code": "USD",
            "cust_id": this.fixtures.tokenData.playerCode,
            "cust_session_id": this.fixtures.tokenData.ipmSessionId,
            "event_id": 0,
            "event_type": "transfer-out",
            "game_status": "settled",
            "game_code": this.fixtures.tokenData.gameCode,
            "game_id": 1000,
            "round_id": "1000",
            "merch_id": this.fixtures.merchantInfo.code,
            "merch_pwd": this.fixtures.merchantInfo.params.password,
            "trx_id": this.fixtures.transferInRequest.transactionId.publicId,
            "timestamp": 1000000,
            "game_type": "normal",
            "platform": "web",
            "actual_bet_amount": 300,
            "actual_win_amount": 200,
            "jp_win_amount": 0,
            "left_amount": 5,
            jp_total_contribution: 0.3204,
            jp_contribution_details: [
                {
                    jackpot_id: "SW-SUPER-LION_Solid_AFUN",
                    jp_contribution: 0.3004,
                    pools: [
                        {
                            is_global_pool: true,
                            jp_contribution: 0.0004,
                            pool_id: "main"
                        },
                        {
                            is_global_pool: true,
                            jp_contribution: 0.3,
                            pool_id: "secondary"
                        }
                    ]
                },
                {
                    jackpot_id: "SW-SUPER-Local-JP",
                    jp_contribution: 0.02,
                    pools: [
                        {
                            is_global_pool: false,
                            jp_contribution: 0.02,
                            pool_id: "main"
                        }
                    ]
                }
            ],
            jp_win_details: [
                {
                    "jackpot_id": "SW-SUPER-LION_Solid_AFUN",
                    "jp_win": 0,
                    "pools": [
                        {
                            "pool_id": "main",
                            "win": 0
                        },
                        {
                            "pool_id": "secondary",
                            "win": 0
                        }
                    ]
                },
                {
                    "jackpot_id": "SW-SUPER-Local-JP",
                    "jp_win": 0,
                    "pools": [
                        {
                            "pool_id": "main",
                            "win": 0
                        }
                    ]
                }
            ],
            actual_bet_count: 88,
            operation_ts: 1
        } as any);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 950
        });

    }

    @test
    public async testTransferOutWithJpWinDetails() {
        BaseSpec.mocker.post(/credit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));

        await this.service.transfer({
                ...this.fixtures.merchantInfo,
                params: {
                    ...this.fixtures.merchantInfo.params, reportJPContributionOnDebitForSeamless: true
                }
            },
            this.fixtures.tokenData,
             {
                 ...this.fixtures.transferOutRequest,
            jackpotDetails: {
                "jackpots": {
                    "FUFISH-JP": {
                        "small": {
                            "win": 51.66,

                        },
                        "medium": {
                            "win": 0,
                        },
                        "big": {
                            "win": 0,
                        }
                    },
                    "FUFISH-JP-KRASIVOE": {
                        "small": {
                            "win": 58.870000000000005,
                        },
                        "medium": {
                            "win": 157.69,
                        },
                        "big": {
                            "win": 1451.35,
                        }
                    }
                }
            } as any
        });
        const transferOutRequest  = this.seamlessAPISpy.post.secondCall.args[MOCK_ARG.BODY];
        expect(transferOutRequest.jp_win_details).deep.equal([
            {
                "jackpot_id": "FUFISH-JP",
                "jp_win": 51.66,
                "pools": [
                    {
                        "pool_id": "small",
                        "win": 51.66
                    },
                    {
                        "pool_id": "medium",
                        "win": 0
                    },
                    {
                        "pool_id": "big",
                        "win": 0
                    }
                ]
            },
            {
                "jackpot_id": "FUFISH-JP-KRASIVOE",
                "jp_win": 1667.91,
                "pools": [
                    {
                        "pool_id": "small",
                        "win": 58.87
                    },
                    {
                        "pool_id": "medium",
                        "win": 157.69
                    },
                    {
                        "pool_id": "big",
                        "win": 1451.35
                    }
                ]
            }
        ]);
    }

    @test
    public async testTransferOutIDS() {
        BaseSpec.mocker.post(/credit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));
        this.getTimestampMock.returns(1000000);

        const result = await this.service.transfer(
            this.fixtures.merchantInfoWithShortCurrency,
            this.fixtures.tokenDataIDS,
            {
                ...this.fixtures.transferOutRequest,
                roundEnded: false
            }
        );

        expect(this.seamlessAPISpy.post.calledTwice).to.be.true;
        expect(this.seamlessAPISpy.post.firstCall.args[MOCK_ARG.URL]).to.be.equal("/api/debit");
        expect(this.seamlessAPISpy.post.firstCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            "amount": 0,
            "currency_code": "IDR",
            "cust_id": this.fixtures.tokenData.playerCode,
            "cust_session_id": this.fixtures.tokenDataIDS.ipmSessionId,
            "event_id": 0,
            "event_type": "transfer-in",
            "game_status": "freegame",
            "game_code": this.fixtures.tokenDataIDS.gameCode,
            "game_id": 1000,
            "round_id": "1000",
            "merch_id": this.fixtures.merchantInfo.code,
            "merch_pwd": this.fixtures.merchantInfo.params.password,
            "trx_id": this.fixtures.transferInRequest.transactionId.publicId,
            "timestamp": 1000000,
            "game_type": "normal",
            "platform": "web",
            operation_ts: 1
        } as any);

        expect(this.seamlessAPISpy.post.secondCall.args[MOCK_ARG.URL]).to.be.equal("/api/credit");
        expect(this.seamlessAPISpy.post.secondCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            "amount": this.fixtures.transferInRequest.amount,
            "currency_code": "IDR",
            "cust_id": this.fixtures.tokenData.playerCode,
            "cust_session_id": this.fixtures.tokenData.ipmSessionId,
            "event_id": 0,
            "event_type": "transfer-out",
            "game_status": "freegame",
            "game_code": this.fixtures.tokenData.gameCode,
            "game_id": 1000,
            "round_id": "1000",
            "merch_id": this.fixtures.merchantInfo.code,
            "merch_pwd": this.fixtures.merchantInfo.params.password,
            "trx_id": this.fixtures.transferInRequest.transactionId.publicId,
            "timestamp": 1000000,
            "game_type": "normal",
            "platform": "web",
            "actual_bet_amount": 300,
            "actual_win_amount": 200,
            "jp_win_amount": 0,
            "left_amount": 5,
            "jp_total_contribution": 0,
            actual_bet_count: undefined,
            operation_ts: 1
        } as any);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 950
        });
    }

    @test
    public async testTransferOutWithSmResult() {
        BaseSpec.mocker.post(/credit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));
        this.getTimestampMock.returns(1000000);

        const result = await this.service.transfer(
            this.fixtures.merchantInfo,
            this.fixtures.tokenData,
            {
                ...this.fixtures.transferOutRequestWithSmResult,
                betsCount: 88,
                roundEnded: true
            }
        );

        expect(this.seamlessAPISpy.post.calledTwice).to.be.true;
        expect(this.seamlessAPISpy.post.firstCall.args[MOCK_ARG.URL]).to.be.equal("/api/debit");
        expect(this.seamlessAPISpy.post.firstCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            "amount": 0,
            "currency_code": "USD",
            "cust_id": this.fixtures.tokenData.playerCode,
            "cust_session_id": this.fixtures.tokenData.ipmSessionId,
            "event_id": 0,
            "event_type": "transfer-in",
            "game_status": "freegame",
            "game_code": this.fixtures.tokenData.gameCode,
            "game_id": 1000,
            "round_id": "1000",
            "merch_id": this.fixtures.merchantInfo.code,
            "merch_pwd": this.fixtures.merchantInfo.params.password,
            "trx_id": this.fixtures.transferInRequest.transactionId.publicId,
            "timestamp": 1000000,
            "game_type": "normal",
            "platform": "web",
            operation_ts: 1
        } as any);

        expect(this.seamlessAPISpy.post.secondCall.args[MOCK_ARG.URL]).to.be.equal("/api/credit");
        expect(this.seamlessAPISpy.post.secondCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            "amount": this.fixtures.transferInRequest.amount,
            "currency_code": "USD",
            "cust_id": this.fixtures.tokenData.playerCode,
            "cust_session_id": this.fixtures.tokenData.ipmSessionId,
            "event_id": 0,
            "event_type": "transfer-out",
            "game_status": "settled",
            "game_code": this.fixtures.tokenData.gameCode,
            "game_id": 1000,
            "round_id": "1000",
            "merch_id": this.fixtures.merchantInfo.code,
            "merch_pwd": this.fixtures.merchantInfo.params.password,
            "trx_id": this.fixtures.transferInRequest.transactionId.publicId,
            "timestamp": 1000000,
            "game_type": "normal",
            "platform": "web",
            "actual_bet_amount": 300,
            "actual_win_amount": 200,
            "jp_win_amount": 0,
            "left_amount": 5,
            "jp_total_contribution": 0,
            "actual_bet_count": 88,
            "sm_result": "0:2;3;4;2;3#2;5;5;2;3#0;7;6;4;3#",
            operation_ts: 1
        } as any);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 950
        });
    }
}

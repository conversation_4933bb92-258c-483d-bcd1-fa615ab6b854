import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { BaseSpec, MOCK_ARG } from "../../../base.spec";
import { SeamlessCertificate } from "../../../../skywind/entities/seamless";
import { getPaymentService, PaymentService } from "../../../../skywind/wallet/services/payment";
import { SinonStub, stub } from "sinon";
import Cache from "../../../../skywind/utils/cache";
import { CertificateService } from "../../../../skywind/utils/getCertificate";
import { SEAMLESS_ERROR_CODE } from "../../../../skywind/errors";
import { testing } from "@skywind-group/sw-utils";
import { BalanceService, getBalanceService } from "../../../../skywind/wallet/services/balance";
import status200 = testing.status200;

@suite
class SSLSpec extends BaseSpec {

    public cache: Cache;

    public getTimestampMock: SinonStub;
    public retryPolicyMock: SinonStub;

    public static readPasswordAndCAfromJSONStub: SinonStub;
    public static readCertAndKeyFilesStub: SinonStub;
    private static certificateService;

    public paymentService: PaymentService;
    public balanceService: BalanceService;

    public static before() {
        super.before();
        SSLSpec.certificateService = CertificateService.getInstance();
        SSLSpec.readPasswordAndCAfromJSONStub = stub(SSLSpec.certificateService, "readPasswordAndCAfromJSON");
        SSLSpec.readCertAndKeyFilesStub = stub(SSLSpec.certificateService, "readCertAndKeyFiles");
    }

    public before() {
        this.cache = Cache.getInstance();
        this.paymentService = getPaymentService(this.apiService);
        this.balanceService = getBalanceService(this.apiService);

        this.getTimestampMock = stub(this.paymentService as any, "getTimestamp");
        this.getTimestampMock.returns(1000000);

        this.retryPolicyMock = stub(this.paymentService, "getRetryPolicy");
        this.retryPolicyMock.returns({
            maxTimeout: 100,
            sleep: 10,
        });

        SSLSpec.readCertAndKeyFilesStub.returns({});
    }

    public after() {
        super.after();
        this.cache.del(this.fixtures.merchantInfo.code);

        this.getTimestampMock.restore();
        this.retryPolicyMock.restore();
    }

    @test
    public async testAbsenceOfCertFilesDoesNotCrashTheFlow() {
        const certData: SeamlessCertificate =
            await SSLSpec.certificateService.getCertificate(this.fixtures.merchantInfo);
        expect(certData).to.exist;
        const dataInCache = this.cache.get(this.fixtures.merchantInfo.code);
        expect(dataInCache).to.exist;
        expect(Object.keys(certData).length).to.eq(0);
    }

    @test
    public async testCAAndPasswordArePresent() {
        SSLSpec.readPasswordAndCAfromJSONStub.returns(Buffer.from("{\"ca\":\"custom ca\", \"password\":\"1234\"}",
            "utf8"));

        const certData: SeamlessCertificate =
            await SSLSpec.certificateService.getCertificate(this.fixtures.merchantInfo);
        expect(certData).to.exist;

        const dataInCache: SeamlessCertificate = this.cache.get(this.fixtures.merchantInfo.code);
        expect(dataInCache).to.exist;
        expect(Object.keys(certData).length).to.eq(2);
        expect(dataInCache.ca).to.eq("custom ca");
        expect(dataInCache.password).to.eq("1234");
    }

    @test
    public async testCAOnlyPresent() {
        SSLSpec.readPasswordAndCAfromJSONStub.returns(Buffer.from("{\"ca\":\"custom ca\"}", "utf8"));
        const certData: SeamlessCertificate =
            await SSLSpec.certificateService.getCertificate(this.fixtures.merchantInfo);
        expect(certData).to.exist;
        const dataInCache: SeamlessCertificate = this.cache.get(this.fixtures.merchantInfo.code);
        expect(dataInCache).to.exist;
        expect(Object.keys(certData).length).to.eq(1);
        expect(dataInCache.ca).to.eq("custom ca");
    }

    @test
    public async testAllFieldsArePresent() {
        SSLSpec.readPasswordAndCAfromJSONStub.returns(Buffer.from(
            "{\"ca\":\"custom ca\", \"password\":\"1234\"}",
            "utf8"));

        SSLSpec.readCertAndKeyFilesStub.returns({
            cert: Buffer.from("custom cert", "utf8"),
            key: Buffer.from("custom key", "utf8"),
        });

        const certData: SeamlessCertificate =
            await SSLSpec.certificateService.getCertificate(this.fixtures.merchantInfo);

        expect(certData).to.exist;
        const dataInCache: SeamlessCertificate = this.cache.get(this.fixtures.merchantInfo.code);
        expect(dataInCache).to.exist;
        expect(Object.keys(certData).length).to.eq(4);
        expect(dataInCache.ca).to.eq("custom ca");
        expect(dataInCache.password).to.eq("1234");
        expect(dataInCache.cert).to.eq("custom cert");
        expect(dataInCache.key).to.eq("custom key");
    }

    @test
    public async testCertAndKeyAndPasswordArePresent() {
        SSLSpec.readPasswordAndCAfromJSONStub.returns(Buffer.from("{\"password\":\"1234\"}", "utf8"));

        SSLSpec.readCertAndKeyFilesStub.returns({
            cert: Buffer.from("custom cert", "utf8"),
            key: Buffer.from("custom key", "utf8"),
        });

        const certData: SeamlessCertificate =
            await SSLSpec.certificateService.getCertificate(this.fixtures.merchantInfo);

        expect(certData).to.exist;
        const dataInCache: SeamlessCertificate = this.cache.get(this.fixtures.merchantInfo.code);
        expect(dataInCache).to.exist;
        expect(Object.keys(certData).length).to.eq(3);
        expect(dataInCache.password).to.eq("1234");
        expect(dataInCache.cert).to.eq("custom cert");
        expect(dataInCache.key).to.eq("custom key");
    }

    @test
    public async testCommitBetHasSSLInfoInHeader() {
        SSLSpec.readPasswordAndCAfromJSONStub.returns(Buffer.from(
            "{\"ca\":\"custom ca\", \"password\":\"1234\"}",
            "utf8"));

        SSLSpec.readCertAndKeyFilesStub.returns({
            cert: Buffer.from("custom cert", "utf8"),
            key: Buffer.from("custom key", "utf8"),
        });

        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));

        const result = await this.paymentService.commitBetPayment(
            this.fixtures.merchantInfoWithSSLRequired,
            this.fixtures.tokenData,
            this.fixtures.paymentRequest
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");
        expect(this.seamlessAPISpy.postMethodOfParentClass.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/debit");

        const httpOptions = this.seamlessAPISpy.postMethodOfParentClass.lastCall.args[2];
        expect(httpOptions.ssl).to.deep.eq({
            "password": "1234",
            "ca": "custom ca",
            "cert": "custom cert",
            "key": "custom key"
        });

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1100
        });
    }

    @test
    public async testGetBalance() {
        SSLSpec.mocker.post(/get_balance/, status200({
            balance: 1000,
            currency_code: "USD",
            error_code: SEAMLESS_ERROR_CODE.SUCCESS
        }));

        SSLSpec.readPasswordAndCAfromJSONStub.returns(Buffer.from(
            "{\"ca\":\"custom ca\", \"password\":\"1234\"}",
            "utf8"));

        SSLSpec.readCertAndKeyFilesStub.returns({
            cert: Buffer.from("custom cert", "utf8"),
            key: Buffer.from("custom key", "utf8"),
        });

        BaseSpec.mocker.post(/debit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            trx_id: "text"
        }));

        const result = await this.balanceService.getBalances(
            this.fixtures.merchantInfoWithSSLRequired,
            this.fixtures.tokenData);

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/get_balance");
        const httpOptions = this.seamlessAPISpy.postMethodOfParentClass.lastCall.args[2];
        expect(httpOptions.ssl).to.deep.eq({
            "password": "1234",
            "ca": "custom ca",
            "cert": "custom cert",
            "key": "custom key"
        });

        expect(result).to.be.deep.equal({
            USD: {
                main: 1000
            }
        });
    }
}

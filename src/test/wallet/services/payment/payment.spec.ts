import { testing } from "@skywind-group/sw-utils";
import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { BaseSpec, MOCK_ARG } from "../../../base.spec";
import { SEAMLESS_ERROR_CODE, } from "../../../../skywind/errors";
import { EVENT_TYPE, SeamlessBetRequest } from "../../../../skywind/entities/seamless";
import { BasePaymentSpec } from "./payment.bet.spec";
import status200 = testing.status200;

@suite
class PaymentSpec extends BasePaymentSpec {

    @test
    public async testZeroPaymentFlag_ZeroBetAndWinAreNotSent() {
        BaseSpec.mocker.post(/get_balance/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS
        }));

        const result = await this.service.commitPayment(
            this.fixtures.merchantInfoWithZeroPaymentsFlag,
            this.fixtures.tokenData,
            {
                ...this.fixtures.paymentRequestWithZeroBetAndWin,
                roundEnded: false
            }
        );

        expect(this.seamlessAPISpy.post.calledTwice).to.be.true;
        expect(this.seamlessAPISpy.post.getCall(0).args[MOCK_ARG.URL]).to.be.equal("api/get_balance");
        expect(this.seamlessAPISpy.post.getCall(1).args[MOCK_ARG.URL]).to.be.equal("api/get_balance");

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1000
        });
    }

    @test
    public async testZeroPaymentFlag_ZeroBetIsSentOnRoundEnd() {
        BaseSpec.mocker.post(/get_balance/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS
        }));
        BaseSpec.mocker.post(/credit/, status200({
            balance: 1000,
            error_code: SEAMLESS_ERROR_CODE.SUCCESS
        }));
        this.getTimestampMock.returns(1000000);

        const result = await this.service.commitPayment(
            this.fixtures.merchantInfoWithZeroPaymentsFlag,
            this.fixtures.tokenData,
            {
                ...this.fixtures.paymentRequestWithZeroBetAndWin,
                roundEnded: true
            }
        );

        expect(this.seamlessAPISpy.post.calledTwice).to.be.true;
        expect(this.seamlessAPISpy.post.getCall(0).args[MOCK_ARG.URL]).to.be.equal("api/get_balance");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/credit");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            cust_session_id: this.fixtures.tokenData.ipmSessionId,
            cust_id: this.fixtures.tokenData.playerCode,
            round_id: this.fixtures.paymentRequest.roundPID,
            game_id: +this.fixtures.paymentRequest.roundId,
            game_type: "normal",
            event_id: 0,
            event_type: EVENT_TYPE.WIN,
            game_code: "sw_mrmnky",
            platform: "web",
            trx_id: "trx",
            amount: 0,
            currency_code: "USD",
            timestamp: 1000000,
            game_status: "settled",
            operation_ts: 1
        } as SeamlessBetRequest);

        expect(result).to.be.deep.equal({
            main: 1000,
            previousValue: 1000
        });
    }
}

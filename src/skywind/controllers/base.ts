import { controller, httpGet } from "inversify-express-utils";
import { inject, injectable } from "inversify";
import { Services } from "../di";
import config from "../config";
import { getEnvironmentInfo } from "@skywind-group/sw-utils";

@controller("/v1")
export class BaseController {
    constructor(@inject(Services.Version) private version: string) {
    }

    @httpGet("/health")
    public getHealth() {
        return {
            serverName: config.server.getName(),
            ...getEnvironmentInfo()
        };
    }

    @httpGet("/version")
    public getVersion() {
        return this.version;
    }
}

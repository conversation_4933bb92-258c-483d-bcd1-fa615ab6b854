import * as express from "express";
import { measures } from "@skywind-group/sw-utils";

const router: express.Router = express.Router();

router.get("/measures/:name", async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
        const measureName: string = req.params.name;
        if (measureName === "all") {
            res.json(await measures.measureProvider.getMeasures());
        } else {
            const measure = await measures.getMeasure(measureName);
            if (measure) {
                res.json(measure);
            } else {
                res.sendStatus(404);
            }
        }
    } catch (err) {
        next(err);
    }
});

export default router;

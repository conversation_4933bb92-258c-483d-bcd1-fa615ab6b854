import { controller, httpPost, requestBody } from "inversify-express-utils";
import { getRegulationService } from "../services/regulation";

@controller("/v1/performRegulatoryAction")
export class RegulationController {

    @httpPost("/")
    public async performRegulatoryAction(@requestBody() { merchantInfo, gameTokenData, request, regulation }: any) {
        return getRegulationService(regulation)
            .performRegulatoryAction(merchantInfo, gameTokenData, request);
    }
}

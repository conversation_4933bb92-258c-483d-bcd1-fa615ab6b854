import { controller, httpPost, requestBody, requestParam } from "inversify-express-utils";
import { Balance, OfflineBonusInfo, PAYMENT_TYPE } from "@skywind-group/sw-wallet-adapter-core";
import { getPaymentService } from "../services/payment";
import { getSeamlessAPIService } from "../services/seamlessAPIService";
import { MerchantInternalError } from "../../errors";

@controller("/v1/payments")
export class PaymentController {

    @httpPost("/")
    public async commitPayment(@requestBody() { merchantInfo, gameTokenData, request, regulation }: any) {
        return await getPaymentService(getSeamlessAPIService(merchantInfo), regulation)
            .commitPayment(merchantInfo, gameTokenData, request);
    }

    @httpPost("/bonus")
    public async commitBonusPayment(
        @requestBody() { merchantInfo, gameTokenData, request, regulation }: any): Promise<Balance> {

        return await getPaymentService(getSeamlessAPIService(merchantInfo), regulation)
            .commitBonusPayment(merchantInfo, gameTokenData, request);
    }

    @httpPost("/transfer")
    public async transfer(@requestBody() { merchantInfo, gameTokenData, request }: any): Promise<Balance> {
        return await getPaymentService(getSeamlessAPIService(merchantInfo))
            .transfer(merchantInfo, gameTokenData, request);
    }

    @httpPost("/:paymentType")
    public async commitSplitPayment(
        @requestParam("paymentType") type: PAYMENT_TYPE,
        @requestBody() { merchantInfo, gameTokenData, request, regulation }: any): Promise<Balance> {

        const service = getPaymentService(getSeamlessAPIService(merchantInfo), regulation);
        switch (type) {
            case PAYMENT_TYPE.BET:
                return service.commitBetPayment(merchantInfo, gameTokenData, request);
            case PAYMENT_TYPE.WIN:
                return service.commitWinPayment(merchantInfo, gameTokenData, request);
            default:
                throw new MerchantInternalError("Invalid payment type");
        }
    }

    @httpPost("/bet/refund")
    public async refundBetPayment(@requestBody() { merchantInfo, gameTokenData, request, regulation }) {
        return await getPaymentService(getSeamlessAPIService(merchantInfo), regulation)
            .refundBetPayment(merchantInfo, gameTokenData, request);
    }

    @httpPost("/bonus/offline")
    public async commitOfflineBonusPayment(
        @requestBody() { merchantInfo, request, regulation }: any): Promise<OfflineBonusInfo> {
        return await getPaymentService(getSeamlessAPIService(merchantInfo), regulation)
            .commitOfflineBonusPayment(merchantInfo, request);
    }
}

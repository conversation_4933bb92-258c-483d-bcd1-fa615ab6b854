import { controller, httpPost, requestBody, } from "inversify-express-utils";
import { getSeamlessAPIService } from "../services/seamlessAPIService";
import { getInfoService } from "../services/info";
import { validate } from "../../middleware";

@controller("/v1/get-page")
export class PageInfoController {

    @httpPost("/", validate({
        "request": { notEmpty: true },
        "request.pageType": { notEmpty: true }
    }))
    public async getPageInfo(@requestBody() { merchantInfo, request }: any) {

        return await getInfoService(getSeamlessAPIService(merchantInfo)).getPage(
            merchantInfo,
            request
        );
    }
}

@controller("/v1/get-player")
export class PlayerInfoController {

    @httpPost("/")
    public async getPlayerInfo(
        @requestBody() { merchantInfo, gameToken }: any) {

        return await getInfoService(getSeamlessAPIService(merchantInfo)).getPlayerInfo(
            merchantInfo,
            gameToken
        );
    }
}

@controller("/v1/get-freebet")
export class FreeBetController {

    @httpPost("/")
    public async getFreebet(
        @requestBody() { merchantInfo, gameToken, freeBetRequest }: any) {

        return await getInfoService(getSeamlessAPIService(merchantInfo)).getFreeBetInfo(
            merchantInfo,
            gameToken,
            freeBetRequest
        );
    }
}

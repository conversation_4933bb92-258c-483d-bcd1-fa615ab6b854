import { controller, httpPost, requestBody } from "inversify-express-utils";
import { getSeamlessAPIService } from "../services/seamlessAPIService";
import { getBrokenGameService } from "../services/brokenGame";

@controller("/v1/logoutGame")
export class LogoutPlayer {

    @httpPost("/")
    public async logoutPlayer(
        @requestBody() { merchantInfo, gameTokenData, request, regulation }: any) {

        return await getBrokenGameService(getSeamlessAPIService(merchantInfo), regulation)
            .logoutGame(merchantInfo, gameTokenData, request);
    }
}

@controller("/v1/finalizeGame")
export class FinalizeGame {

    @httpPost("/")
    public async finalizeGame(
        @requestBody() { merchantInfo, gameTokenData, request, regulation }: any) {

        return await getBrokenGameService(getSeamlessAPIService(merchantInfo), regulation)
            .finalizeGame(merchantInfo, gameTokenData, request);

    }
}

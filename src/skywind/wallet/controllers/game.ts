import { controller, httpPost, requestBody, requestParam } from "inversify-express-utils";
import { getGameService } from "../services/game";
import { getSeamlessAPIService } from "../services/seamlessAPIService";

@controller("/v1/games")
export class StartGameController {

    @httpPost("/keep-alive")
    public async keepAlive(
        @requestBody() { merchantInfo, gameToken }: any) {

        return await getGameService(getSeamlessAPIService(merchantInfo))
            .keepAlive(merchantInfo, gameToken);

    }

    @httpPost("/login-terminal")
    public async verifyTerminalTicket(
        @requestBody() { merchantInfo, initRequest }: any) {

        return await getGameService(getSeamlessAPIService(merchantInfo))
            .loginTerminalPlayer(merchantInfo, initRequest);

    }

    @httpPost("/:gameCode/url")
    public async createGameUrl(
        @requestParam("gameCode") gameCode: string,
        @requestBody() { merchantInfo, providerCode, providerGameCode, initRequest, playerInfo, regulation }: any) {

        return await getGameService(getSeamlessAPIService(merchantInfo), regulation).createGameUrl(
            merchantInfo,
            gameCode,
            providerCode,
            providerGameCode,
            initRequest,
            playerInfo
        );
    }

    @httpPost("/:gameCode/token")
    public async getTokenData(
        @requestParam("gameCode") gameCode: string,
        @requestBody() { merchantInfo, startGameToken, currency, transferEnabled, regulation }: any) {

        return await getGameService(getSeamlessAPIService(merchantInfo), regulation)
            .getGameTokenInfo(merchantInfo, startGameToken, currency, transferEnabled);

    }

    @httpPost("/:gameCode/start-token")
    public async getStartGameToken(
        @requestParam("gameCode") gameCode: string,
        @requestBody() { merchantInfo, initRequest, providerCode, providerGameCode }: any) {

        return await getGameService(getSeamlessAPIService(merchantInfo))
            .getStartGameTokenData(merchantInfo, gameCode, providerCode, providerGameCode, initRequest);

    }
}

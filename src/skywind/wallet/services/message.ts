import {
    ClientMessageType,
    ExtraMessageImpl,
    MrchExtraDataImpl,
    PopupButtonGameActions,
    PopupButtonImpl
} from "@skywind-group/sw-wallet-adapter-core";

import { injectable } from "inversify";
import { SeamlessCMAContainer, SeamlessCMAMessage } from "../../entities/seamless";
import { lazy } from "@skywind-group/sw-utils";

export interface MessageService {
    isMessageExists(container: SeamlessCMAContainer): boolean;
    getExtraData(...containers: SeamlessCMAContainer[]): MrchExtraDataImpl;
}

@injectable()
export class CMAMessageService implements MessageService {
    public isMessageExists(container: SeamlessCMAContainer): boolean {
        return !!(Array.isArray(container.messages) &&
            container.messages.filter(msg => msg.msgType.toLowerCase() === "message"));
    }

    public getExtraData(...containers: SeamlessCMAContainer[]): MrchExtraDataImpl {
        const extraData = MrchExtraDataImpl.create();

        const okButton = PopupButtonImpl.create()
            .setLabel("OK")
            .setGameAction(PopupButtonGameActions.continue)
            .setTranslate(true);

        const closePopupButton = PopupButtonImpl.create()
            .setLabel("Close")
            .setGameAction(PopupButtonGameActions.continue)
            .setTranslate(true);

        for (const container of containers) {
            if (!this.isMessageExists(container)) {
                continue;
            }

            const messages = container.messages.filter(msg => msg.msgType.toLowerCase() === "message");
            for (const message of messages) {
                const msgType = this.getMessageType(message);
                const ipmCMAMessage = ExtraMessageImpl.create()
                    .setButtons(msgType === ClientMessageType.toaster ? [closePopupButton] : [okButton])
                    .setTranslate(false)
                    .setMessage(message.message)
                    .setMessageType(msgType);

                if ("title" in message) {
                    ipmCMAMessage.setMessageTitle(message.title);
                }

                extraData.addExtraMessage(ipmCMAMessage);
            }
        }

        if (extraData.messageArray?.length) {
            return extraData;
        }
    }

    private getMessageType(message: SeamlessCMAMessage) {
        if ("nonIntrusive" in message) {
            if (message.nonIntrusive) {
                return ClientMessageType.toaster;
            } else {
                return ClientMessageType.info;
            }
        }
    }
}

export const messageServiceContainer = lazy(() => new CMAMessageService());

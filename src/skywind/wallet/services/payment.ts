import {
    Balance,
    CannotCompletePayment,
    DefaultMerchantPaymentService,
    DeferredPaymentOperation,
    InternalAPIService,
    InterruptSocket,
    ITrxId,
    JackpotDetails,
    JackpotIdDetails,
    JackpotPoolDetails,
    MerchantBonusApiAdditionalFields,
    MerchantInfo,
    MerchantTransferRequest,
    OfflineBonusInfo,
    OfflineBonusPaymentRequest,
    PaymentRequest,
    RefundBetRequest,
    RegisterRoundRequest,
    RegisterRoundResponse
} from "@skywind-group/sw-wallet-adapter-core";
import {
    EVENT_TYPE,
    SeamlessAuthTokenData,
    SeamlessBetRequest,
    SeamlessCustomerData,
    SeamlessMerchantData,
    SeamlessOfflineBonusRequest,
    SeamlessOfflineBonusResponse,
    SeamlessPaymentRequest,
    SeamlessPaymentResponse,
    SeamlessPlayerTokenData,
    SeamlessRollbackRequest,
    SeamlessTransferRequest,
    SeamlessWinRequest,
    TransferOutRequest
} from "../../entities/seamless";
import { SeamlessAPIService } from "./seamlessAPIService";
import {
    GameTokenExpired,
    retryBetCondition,
    retryCondition,
    RollbackTransactionError,
    SEAMLESS_ERROR_CODE,
    TransactionNotFound,
    wrapBetError,
    wrapRollbackError,
    wrapWinError
} from "../../errors";
import { CurrencyConverter } from "../../utils/currencyConverter";
import { calculation, logging, measures, publicId, retry } from "@skywind-group/sw-utils";
import config from "../../config";
import { BalanceService, getBalanceService } from "./balance";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import {
    BonusRequest,
    DebitContributionDetails,
    JackpotWinDetails,
    PromoRequest
} from "../../entities/sw";
import { applyRegulation } from "../../regulations";
import { updateHash } from "../../utils/updateHash";
import measure = measures.measure;
import Logger = logging.Logger;

const DEFAULT_PRECISION = 6;

const SM_RESULT_EVENT_TYPES = [EVENT_TYPE.WIN, EVENT_TYPE.FREEBET_WIN, EVENT_TYPE.TRANSFER_OUT];

export class PaymentService extends DefaultMerchantPaymentService<SeamlessAuthTokenData> {
    constructor(public service: SeamlessAPIService,
                public balanceService: BalanceService,
                protected log: Logger,
                public internalApiService: InternalAPIService,
                public retryPolicy = config.seamless.retryPolicy) {
        super();
    }

    public getRetryPolicy() {
        return this.retryPolicy;
    }

    @measure({ name: "PaymentService.commitPayment", isAsync: true })
    public async commitPayment(merchant: MerchantInfo,
                               tokenData: SeamlessAuthTokenData,
                               request: PaymentRequest & BonusRequest & PromoRequest): Promise<Balance> {

        const betBalance = await this.commitBetPayment(merchant, tokenData, request);
        const winBalance = await this.commitWinPayment(merchant, tokenData, request);

        const balance = { ...betBalance, ...winBalance };

        const win = request.win || 0;
        const bet = request.bet || 0;
        const previousValue = request.freeBetCoin ? balance.main - win : balance.main - win + bet;
        balance.previousValue = Currencies.get(tokenData.currency).toFixedByExponent(previousValue);

        return balance;

    }

    @measure({ name: "PaymentService.registerRound", isAsync: true })
    public async registerRound(request: RegisterRoundRequest): Promise<RegisterRoundResponse> {
        return await this.service.put<RegisterRoundResponse>(
            "/api/register_round",
            request,
        );
    }

    @measure({ name: "PaymentService.commitBetPayment", isAsync: true })
    public async commitBetPayment(merchant: MerchantInfo,
                                  tokenData: SeamlessAuthTokenData,
                                  request: PaymentRequest & BonusRequest & PromoRequest): Promise<Balance> {
        if (this.isZeroBetPaymentThatMustNotBeSent(request, merchant)) {
            return this.getBalance(merchant, tokenData, undefined, 0, request);
        }

        const bet = request.bet || 0;
        const response = await this.postDebit(merchant, tokenData, request, bet);

        try {
            const amount = request.freeBetCoin ? 0 : bet;
            return await this.getBalance(merchant, tokenData, response, amount);
        } catch (err) {
            if (err instanceof GameTokenExpired && response.error_code === SEAMLESS_ERROR_CODE.DUPLICATE_TRX_ERROR) {
                // Handle case when bet retried - duplicate transaction error code, but we cannot get balance
                // This payment will be retried on next login
                throw new CannotCompletePayment();
            }
            throw err;
        }
    }

    // I hope we dont break any freebet logic with this
    private isZeroBetPaymentThatMustNotBeSent(paymentRequest: PaymentRequest, merchant: MerchantInfo): boolean {
        return merchant.params.dontSendZeroPayments && !paymentRequest.bet && paymentRequest.eventId !== 0;
    }

    private async postDebit(merchant: MerchantInfo,
                            tokenData: SeamlessAuthTokenData,
                            request: PaymentRequest & BonusRequest & PromoRequest,
                            amount: number): Promise<SeamlessPaymentResponse> {
        const eventType = this.getBetEventType(request);

        const data = this.getPaymentData<SeamlessBetRequest>(eventType, amount, merchant, tokenData, request);
        if (request.freeBetCoin) {
            data.free_bet_coin = request.freeBetCoin;
        }

        this.log.debug({ data }, "Commit bet");

        const isNeedRetry = merchant.params.refundBetInsteadOfRetry === false;
        const action = () => this.service
            .doPost<SeamlessPaymentResponse>("api/debit", data, merchant);

        return retry(
            this.getRetryPolicy(),
            action,
            retryBetCondition(isNeedRetry),
            wrapBetError(isNeedRetry, tokenData, request, merchant.params.isRefundOnBadRequestRequired)
        );
    }

    @measure({ name: "PaymentService.commitWinPayment", isAsync: true })
    public async commitWinPayment(merchant: MerchantInfo,
                                  tokenData: SeamlessAuthTokenData,
                                  request: PaymentRequest & BonusRequest & PromoRequest): Promise<Balance> {
        if (this.isZeroWinPaymentThatMustNotBeSent(request, merchant)) {
            return this.getBalance(merchant, tokenData, undefined, 0, request);
        }

        await this.sendZeroBetForSrtWinIfNeeded(merchant, tokenData, request);

        const win = request.win || 0;
        const response = await this.postWin(merchant, tokenData, request, win);

        return this.getBalance(merchant, tokenData, response, -win);
    }

    /**
     * This method is to cover scenarios for SRT redeem payouts, when 'credit' payout that is not accompanied by
     * preceeding bet is rejected by operator, so we explicitly send the 0-bet to try cover this gap.
     */
    private async sendZeroBetForSrtWinIfNeeded(merchant: MerchantInfo,
                                               authToken: SeamlessAuthTokenData,
                                               creditRequest: PaymentRequest): Promise<void> {
        // do nothing if its not SRT payment or operator explicitly requested about no Zero bets
        if (!creditRequest.grcRedeemInfo || merchant.params.dontSendZeroPayments) {
            return;
        }

        try {
            await this.postDebit(merchant, authToken, creditRequest, 0);
        } catch (error) {
            // suppress - this bet is synthetic and may have been not needed
            this.log.warn(error, "Zero bet for SRT payout failed");
        }
    }

    private isZeroWinPaymentThatMustNotBeSent(paymentRequest: PaymentRequest, merchant: MerchantInfo): boolean {
        return merchant.params.dontSendZeroPayments &&
            !paymentRequest.win &&
            this.getGameStatus(paymentRequest) !== "settled";
    }

    private async postWin(merchant: MerchantInfo,
                          tokenData: SeamlessAuthTokenData,
                          request: PaymentRequest & BonusRequest & PromoRequest,
                          amount: number): Promise<SeamlessPaymentResponse> {
        const eventType = this.getWinEventType(request);

        const data = this.getPaymentData<SeamlessWinRequest>(eventType, amount, merchant, tokenData, request);
        data.game_status = this.getGameStatus(request);

        if (request.isJPWin) {
            data.jp_win = true;
            data.jp_ids = this.getWinJackpotIds(request);

            if (merchant.params.reportJPWinStatisticOnCreditForSeamless) {
                this.decorateRequestWithJpWinStatistic(data, request.jackpotDetails);
            }
        }

        this.log.debug({ data }, "Commit win");

        const action = () => this.service.doPost<SeamlessPaymentResponse>("api/credit", data, merchant);

        try {
            return await retry(
                this.getRetryPolicy(),
                action,
                retryCondition(tokenData),
                wrapWinError(request.offlineRetry)
            );
        } catch (err) {
            this.interruptSocketForLiveGame(request, err, tokenData);
            throw err;
        }
    }

    /**
     * Interrupt the socket for live game to prevent any other errors and popups
     * caused with retries or something else.
     */
    private interruptSocketForLiveGame(
        request: PaymentRequest,
        error,
        tokenData?: SeamlessAuthTokenData
    ) {
        if (!request.offlineRetry && tokenData?.isLiveGame) {
            if (!(error instanceof RollbackTransactionError)) {
                throw new InterruptSocket(error.message);
            }
        }
    }

    public getTrxIdForPayload(merchant: MerchantInfo, trxId: ITrxId): string | number {
        if (merchant.params?.sendNumericTrxId === true) {
            return trxId.serialId;
        }
        return trxId.publicId;
    }

    private async rollbackTransaction(merchant: MerchantInfo,
                                      tokenData: SeamlessAuthTokenData,
                                      request: PaymentRequest & BonusRequest): Promise<SeamlessPaymentResponse> {
        const gameType = this.getGameType(request);
        const data: SeamlessRollbackRequest = {
            currency_code: CurrencyConverter.toIPMCurrency(tokenData.currency, merchant),
            game_code: tokenData.gameCode,
            trx_id: this.getTrxIdForPayload(merchant, request.transactionId),
            game_id: +request.roundId,
            event_id: request.eventId ?? 0,
            event_type: EVENT_TYPE.ROLLBACK,
            game_type: gameType,
            // If the game type is something other than normal, like a free spin, and we do a rollback,
            // it means that this operation is supposed to be repeated, so we can set the
            // game status as the current game type; this is required for the last free spin in a round
            // rollback scenario, in order to not incorrectly send "settled"
            game_status: gameType !== "normal" ? gameType : this.getGameStatus(request),
            timestamp: this.getTimestamp(),
            round_id: request.roundPID || request.roundId,
            ...this.getSensitiveData(merchant, tokenData),
            operation_ts: request.transactionId.timestamp,
        };
        this.log.info({ data }, "Commit rollback");

        const action = () => this.service.doPost<SeamlessPaymentResponse>("api/rollback", data, merchant);

        try {
            return await retry(this.getRetryPolicy(), action, retryCondition(tokenData), wrapRollbackError);
        } catch (err) {
            this.interruptSocketForLiveGame(request, err, tokenData);
            throw err;
        }
    }

    private getBetEventType(request: PaymentRequest & BonusRequest): EVENT_TYPE {
        if (request.operation === "finalize-game") {
            return EVENT_TYPE.ROUND_STATISTICS;
        }
        if (request.bonus) {
            return EVENT_TYPE.BONUS;
        }

        if (request.promoType) {
            return request.promoType as EVENT_TYPE;
        }

        return this.isFreeBet(request) ? EVENT_TYPE.FREEBET : EVENT_TYPE.BET;
    }

    private getWinEventType(request: PaymentRequest & BonusRequest): EVENT_TYPE {
        if (request.operation === "finalize-game") {
            return EVENT_TYPE.ROUND_STATISTICS;
        }
        if (request.bonus) {
            return EVENT_TYPE.BONUS;
        }

        if (request.promoType) {
            return request.promoType as EVENT_TYPE;
        }

        return this.isFreeBet(request) ? EVENT_TYPE.FREEBET_WIN : EVENT_TYPE.WIN;
    }

    private getPaymentData<I extends SeamlessPaymentRequest>(eventType: EVENT_TYPE,
                                                             amount: number,
                                                             merchant: MerchantInfo,
                                                             tokenData: SeamlessAuthTokenData,
                                                             request: PaymentRequest & BonusRequest & PromoRequest): I {

        const paymentData = {
            currency_code: CurrencyConverter.toIPMCurrency(tokenData.currency, merchant),
            game_code: tokenData.gameCode,
            trx_id: this.getTrxIdForPayload(merchant, request.transactionId),
            game_id: +request.roundId,
            round_id: request.roundPID || request.roundId,
            timestamp: this.getTimestamp(),
            event_id: request.eventId || 0,
            platform: this.getPlatform(request.deviceId),
            game_type: this.getGameType(request),
            amount,
            event_type: eventType,
            ...this.getSensitiveData(merchant, tokenData),
            operation_ts: request.transactionId.timestamp,
        } as I;

        if (request.extraData) {
            paymentData.extraData = request.extraData;
        }

        if (request.finalizationType) {
            paymentData.is_finalization_payment = true;
        }

        if (request.promoId) {
            paymentData.promo_id = request.promoId;

            if (Number.isFinite(+request.promoId)) {
                paymentData.promo_pid = publicId.instance.encode(+request.promoId);
            }
        }

        if (request.typePromo && merchant?.params?.isPromoTypeEnabled) {
            paymentData.promo_type = request.typePromo;
        }

        if (this.isFreeBet(request) && merchant.params.isPromoInternal) {
            const promoId = request.freeBetBalance?.activePromoId;
            if (promoId !== undefined) {
                paymentData.promo_id = promoId.toString();
                paymentData.promo_pid = publicId.instance.encode(+promoId);
            }
        }

        if (this.isFreeBet(request) && request.freeBetBalance?.externalId) {
            paymentData.promo_external_id = request.freeBetBalance.externalId;
        }

        if (request.bonus) {
            paymentData.sub_trx_type = request.promoType;
        }

        if (request.operation === "deferred-payment" && merchant.params.supportPromoDistributionType) {
            paymentData.distribution_type = (request as DeferredPaymentOperation).deferredPayment.distributionType;
        }

        if (amount && eventType === EVENT_TYPE.BET && merchant.params.reportJPContributionOnDebitForSeamless) {
            if (request.totalJpContribution) {
                paymentData.jp_contribution = request.totalJpContribution;
            }

            // jackpotDetails field should be present if entity has flag deferredContribution
            if (request.jackpotDetails) {
                this.decoratePaymentDataWithContributionStatistic(paymentData, request.jackpotDetails);
            }
        }

        if (request.grcRedeemInfo) {
            paymentData.grc_redeem_info = {
                stars: request.grcRedeemInfo.stars,
                amount: request.grcRedeemInfo.amount
            };
        }

        if (SM_RESULT_EVENT_TYPES.includes(eventType) && request.roundEnded && request.smResult) {
            paymentData.sm_result = request.smResult;
        }

        return paymentData;
    }

    // maps jackpotDetails field to something more readable for seamless operators
    // example: [{
    //    jackpot_id: "SW-SUPER-LION_Solid_AFUN",
    //    pools: [{
    //        poolId: "main",
    //        is_global_pool: true,
    //        jp_contribution: 0.004 - pool's contribution
    //    }],
    //    jp_contribution: 0.004 - sum of pools contributions
    // }]
    public decoratePaymentDataWithContributionStatistic(paymentData: any, jackpotDetails: JackpotDetails) {
        const jackpotArray: DebitContributionDetails[] = [];
        const jackpots = jackpotDetails && jackpotDetails.jackpots || {};
        const precision = jackpotDetails?.contributionPrecision || DEFAULT_PRECISION;
        for (const jackpotId of Object.keys(jackpots)) {
            const jackpotInfo: DebitContributionDetails = {
                jackpot_id: jackpotId,
                pools: [],
                jp_contribution: 0
            };
            jackpotArray.push(jackpotInfo);

            const pools: JackpotIdDetails = jackpots[jackpotId];
            for (const poolId of Object.keys(pools)) {
                const pool: JackpotPoolDetails = pools[poolId];
                const progressive = pool?.contribution?.progressive || 0;
                const seed = pool?.contribution?.seed || 0;
                const normalizedContribution = calculation.normalizeAmountByPrecision(precision, seed + progressive);

                jackpotInfo.jp_contribution = calculation.normalizeAmountByPrecision(precision,
                    jackpotInfo.jp_contribution + normalizedContribution);
                jackpotInfo.pools.push({
                    pool_id: poolId,
                    is_global_pool: !pool.isLocal,
                    jp_contribution: normalizedContribution
                });
            }
        }

        if (jackpotArray.length) {
            paymentData.jp_contribution_details = jackpotArray;
        }
    }

    public getTimestamp() {
        return Math.floor(Date.now() / 1000);
    }

    public getSensitiveData(merchant: MerchantInfo,
                            tokenData: SeamlessPlayerTokenData): SeamlessMerchantData & SeamlessCustomerData {
        const data: SeamlessMerchantData & SeamlessCustomerData = {
            merch_id: merchant.code,
            merch_pwd: merchant.params.password
        };

        if (tokenData?.playerCode) {
            data.cust_id = tokenData.playerCode;
        }
        if (tokenData?.ipmSessionId) {
            data.cust_session_id = tokenData.ipmSessionId;
        }

        return data;
    }

    private async getBalance(merchant: MerchantInfo,
                             tokenData: SeamlessAuthTokenData,
                             response: SeamlessPaymentResponse | undefined,
                             amount: number,
                             request?: PaymentRequest): Promise<Balance | undefined> {
        try {
            const balance = await this.checkAndParseBalance(merchant, tokenData, response);
            if (balance) {
                const previousValue = balance.main + amount;
                balance.previousValue = Currencies.get(tokenData.currency).toFixedByExponent(previousValue);
            }
            return balance;
        } catch (err) {
            // Return zero balance in case of token expired errors during offline retries or finalization payments
            if (err instanceof GameTokenExpired && request?.offlineRetry) {
                return {
                    main: 0
                };
            }
            throw err;
        }
    }

    private async checkAndParseBalance(merchant: MerchantInfo,
                                       tokenData: SeamlessAuthTokenData,
                                       response?: SeamlessPaymentResponse,
                                       fetchBalance: boolean = true): Promise<Balance | undefined> {
        if (response?.balance !== undefined) {
            return this.balanceService.parseBalance(merchant, response);
        } else if (fetchBalance) {
            return await this.balanceService.getBalance(merchant, tokenData);
        }
    }

    private getWinJackpotIds(payment: PaymentRequest): string[] {
        const jackpotStatistic = payment?.jackpotWinDetails;
        if (!jackpotStatistic) {
            return undefined;
        }

        const jpIds = Object.keys(jackpotStatistic);
        if (jpIds.length) {
            return jpIds;
        }
    }

    @measure({ name: "PaymentService.refundBetPayment", isAsync: true })
    public async refundBetPayment(merchant: MerchantInfo,
                                  gameToken: SeamlessAuthTokenData,
                                  request: RefundBetRequest): Promise<Balance> {
        let response: SeamlessPaymentResponse;
        const zeroBetDisabled = request.bet === 0 && merchant.params.dontSendZeroPayments;
        if (!zeroBetDisabled) {
            try {
                response = await this.rollbackTransaction(merchant, gameToken, request);
            } catch (err) {
                if (err instanceof RollbackTransactionError || err instanceof TransactionNotFound) {
                    // ignore error if rollback successful
                    // return zero balance directly instead of calling getBalance
                    // operators will return error on getBalance because when a rollback is performed,
                    // the game session is already closed on their side; all subsequent getBalance calls will fail
                    return {
                        main: 0
                    };
                }
                throw err;
            }
        }
        try {
            return await this.getBalance(merchant, gameToken, response, -request.bet);
        } catch (err) {
            // ignore error for zero-bet rollback
            if (!response) {
                return {
                    main: 0
                };
            }
            throw err;
        }
    }

    @measure({ name: "PaymentService.transfer", isAsync: true })
    public async transfer(merchant: MerchantInfo,
                          gameToken: SeamlessAuthTokenData,
                          request: MerchantTransferRequest): Promise<Balance> {

        if (request.operation === "transfer-in") {
            return await this.transferIn(merchant, gameToken, request);
        } else if (request.operation === "transfer-out") {
            return await this.transferOut(merchant, gameToken, request);
        }

    }

    @measure({ name: "PaymentService.commitBonusPayment", isAsync: true })
    public commitBonusPayment(merchant: MerchantInfo,
                              gameToken: SeamlessAuthTokenData,
                              request: PaymentRequest): Promise<Balance> {
        return this.commitPayment(merchant, gameToken, { ...request, bonus: true });
    }

    @measure({ name: "PaymentService.commitOfflineBonusPayment", isAsync: true })
    public async commitOfflineBonusPayment(merchant: MerchantInfo,
                                           request: OfflineBonusPaymentRequest): Promise<OfflineBonusInfo> {
        const data: SeamlessOfflineBonusRequest = {
            merch_id: merchant.code,
            merch_pwd: merchant.params.isPasswordBonusAuth ? merchant.params.password : undefined,
            cust_id: request.playerCode,
            amount: request.amount,
            currency_code: request.currencyCode,
            promo_type: request.promoType,
            trx_id: this.getTrxIdForPayload(merchant, request.transactionId),
            promo_id: request.promoId,
            timestamp: this.getTimestamp(),
        };

        this.additionalOfflineBonusPaymentData(data, merchant, request);

        if (request["externalPromoId"]) { // TODO, change from ver 4.62
            data.promo_id = request["externalPromoId"] + "";
        } else if (request.promoId && Number.isFinite(+request.promoId)) {
            data.promo_pid = publicId.instance.encode(+request.promoId);
        }

        const entitySettings = await this.internalApiService.getMerchantEntitySettings(merchant.code, merchant.type);
        const shouldHashParamsBeLimited = entitySettings && Array.isArray(entitySettings.hashParams) &&
            entitySettings.hashParams.length;
        const dataWithHashParamsOnly = {};

        if (shouldHashParamsBeLimited) {
            entitySettings.hashParams.forEach(param => {
                dataWithHashParamsOnly[param] = data[param];
            });
        }

        let hash;

        if (!merchant.params.isPasswordBonusAuth) {
            hash = shouldHashParamsBeLimited ?
                updateHash(dataWithHashParamsOnly, merchant.params.password) :
                updateHash(data, merchant.params.password);
        }

        const response = await this.service.doPost<SeamlessOfflineBonusResponse>(
            "/api/bonus",
            {
                ...data,
                hash,
            },
            merchant);
        return {
            balance: { main: response.balance },
            externalTrxId: response.trx_id
        };
    }

    private additionalOfflineBonusPaymentData(
        data: any,
        merchant: MerchantInfo,
        bonusRequest: OfflineBonusPaymentRequest
    ): void {

        if (merchant.params.supportPromoDistributionType) {
            if (bonusRequest.distributionType) {
                data.distribution_type = bonusRequest.distributionType;
            } else {
                this.log.warn(
                    bonusRequest,
                    "supportPromoDistributionType is enabled in merchant params, " +
                    "but distributionType does not exist in the request"
                );
            }
        }

        const additionalFields: MerchantBonusApiAdditionalFields = merchant.params?.bonusApiAdditionalFields;
        if (!additionalFields) {
            return;
        }

        if (additionalFields?.supportOperationTs) {
            const ts = bonusRequest.transactionId?.timestamp;
            if (ts) {
                data.operation_ts = ts;
            } else {
                this.log.warn(
                    bonusRequest,
                    "supportOperationTs is enabled in merchant params, " +
                    "but timestamp does not exist in the request"
                );
            }
        }
    }

    private async transferIn(merchant: MerchantInfo,
                             gameToken: SeamlessAuthTokenData,
                             request: MerchantTransferRequest,
                             fetchBalance?: boolean): Promise<Balance> {

        const transferInData = this.getPaymentData<SeamlessTransferRequest>(
            EVENT_TYPE.TRANSFER_IN,
            request.amount,
            merchant,
            gameToken,
            request
        );
        transferInData.game_status = request.gameStatus || "freegame";

        const response = await this.service.doPost<SeamlessPaymentResponse>("/api/debit", transferInData, merchant);
        const balance: Balance = await this.checkAndParseBalance(merchant, gameToken, response, fetchBalance);
        balance.previousValue = balance.main + request.amount;
        return balance;
    }

    private async transferOut(merchant: MerchantInfo,
                              gameToken: SeamlessAuthTokenData,
                              request: MerchantTransferRequest): Promise<Balance> {
        // this transfer protects us from case when player tries to make several consequent transfer-out
        try {
            await this.transferIn(merchant, gameToken, { ...request, amount: 0 });
        } catch (err) {
            if (err instanceof GameTokenExpired) {
                throw new CannotCompletePayment();
            }

            throw err;
        }

        const transferOutData = this.getTransferOutData(merchant, gameToken, request);
        let ipmCredit: SeamlessPaymentResponse;

        try {
            ipmCredit = await this.service.doPost<SeamlessPaymentResponse>(
                "/api/credit",
                transferOutData,
                merchant);
        } catch (err) {
            if (err instanceof GameTokenExpired) {
                throw new CannotCompletePayment();
            }

            throw err;
        }

        const balance = await this.checkAndParseBalance(merchant, gameToken, ipmCredit);
        balance.previousValue = balance.main - request.amount;

        return balance;
    }

    private getTransferOutData(merchant: MerchantInfo,
                               gameToken: SeamlessAuthTokenData,
                               request: MerchantTransferRequest): TransferOutRequest {
        const transferOutData = {
            left_amount: request.previousAmount - request.amount || 0,
            actual_bet_amount: request.actualBetAmount !== undefined ? request.actualBetAmount : 0,
            actual_win_amount: request.actualWinAmount !== undefined ? request.actualWinAmount : 0,
            jp_win_amount: request.jpWinAmount !== undefined ? request.jpWinAmount : 0,
            jp_total_contribution: request.totalJpContribution !== undefined ? request.totalJpContribution : 0,
            game_type: "normal",
            game_status: this.getGameStatus(request),
            actual_bet_count: request.betsCount,
            operation_ts: request.transactionId.timestamp
        };

        if (merchant.params.reportJPContributionOnDebitForSeamless && request.jackpotDetails) {
            this.decoratePaymentDataWithContributionStatistic(transferOutData, request.jackpotDetails);
            this.decorateRequestWithJpWinStatistic(transferOutData, request.jackpotDetails);
        }

        const commonPaymentData = this.getPaymentData(
            EVENT_TYPE.TRANSFER_OUT, request.amount, merchant, gameToken, request);

        return {
            ...commonPaymentData,
            ...transferOutData
        };
    }

    private isFreeBet(payment: PaymentRequest) {
        return payment.freeBetCoin || payment.freeBetMode;
    }

    public decorateRequestWithJpWinStatistic(paymentData: any, jackpotDetails: JackpotDetails) {
        const jackpots = jackpotDetails && jackpotDetails.jackpots || {};
        const precision = jackpotDetails && jackpotDetails.contributionPrecision || DEFAULT_PRECISION;
        const result: JackpotWinDetails[] = Object.entries(jackpots)
            .map(([jackpotId, details]) => {
                const pools = Object.entries(details).map(([poolId, poolDetails]) => {
                    return {
                        pool_id: poolId,
                        win: calculation.normalizeAmountByPrecision(precision, poolDetails.win)
                    };
                });
                const jpWin = Object.values(details).reduce((acc: number, currentPool: JackpotPoolDetails) => {
                    return calculation.safeAddWithPrecision(precision, acc, currentPool.win);
                }, 0);
                const eachJpDetails: JackpotWinDetails = {
                    jackpot_id: jackpotId,
                    pools: pools,
                    jp_win: jpWin
                };
                return eachJpDetails;
            });

        if (result.length) {
            paymentData.jp_win_details = result;
        }
    }
}

const logger = logging.logger("payment-service");

export function getPaymentService(apiService: SeamlessAPIService, regulation?): PaymentService {
    const internalApiService = new InternalAPIService(config.internalMAPIUrl);
    const service = new PaymentService(apiService, getBalanceService(apiService), logger, internalApiService);
    return applyRegulation(service, regulation);
}

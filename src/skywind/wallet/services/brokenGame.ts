import {
    Balance,
    BrandFinalizationType,
    DefaultMerchantBrokenGameService,
    FinalizeGameRequest,
    GameLoginRequest,
    GameLogoutRequest,
    GameLogoutResponse,
    MerchantInfo
} from "@skywind-group/sw-wallet-adapter-core";
import { EVENT_TYPE, SeamlessAuthTokenData, SeamlessBalanceResponse } from "../../entities/seamless";
import { SeamlessAPIService } from "./seamlessAPIService";
import { logging, measures } from "@skywind-group/sw-utils";
import { getPaymentService, PaymentService } from "./payment";
import { applyRegulation } from "../../regulations";
import { CurrencyConverter } from "../../utils/currencyConverter";
import Logger = logging.Logger;
import measure = measures.measure;

export class BrokenGameService extends DefaultMerchantBrokenGameService<SeamlessAuthTokenData> {
    constructor(public service: SeamlessAPIService, public paymentService: PaymentService, protected log: Logger) {
        super();
    }

    public async finalizeGame(merchant: MerchantInfo,
                              gameTokenData: SeamlessAuthTokenData,
                              finalizeGameRequest: FinalizeGameRequest): Promise<Balance> {
        if (finalizeGameRequest.finalizationType === BrandFinalizationType.OFFLINE_PAYMENTS) {
            try {
                return await this.paymentService.balanceService.getBalance(merchant, gameTokenData);
            } catch (e) {
                return {
                    main: 0
                };
            }
        }
        if (finalizeGameRequest.finalizationType === BrandFinalizationType.ROUND_STATISTICS) {
            return this.finalizeWithRoundStatistics(merchant, gameTokenData, finalizeGameRequest);
        }
        if (finalizeGameRequest.finalizationType === BrandFinalizationType.FORCE_FINISH) {
            return this.finalizeWithForceFinish(merchant, gameTokenData, finalizeGameRequest);
        }

        this.log.warn((`Finalization type ${finalizeGameRequest.finalizationType} is not supported`));
        return {
            main: 0
        };
    }

    @measure({ name: "BrokenGameService.finalizeWithRoundStatistics", isAsync: true })
    public async finalizeWithRoundStatistics(merchant: MerchantInfo,
                                             tokenData: SeamlessAuthTokenData,
                                             finalizeGameRequest: FinalizeGameRequest): Promise<Balance> {

        const finalizeRoundData = this.makeResolveRoundIPMrequest(
            merchant,
            tokenData,
            finalizeGameRequest,
            EVENT_TYPE.ROUND_STATISTICS);

        return this.commitRoundResolve(merchant, finalizeRoundData);
    }

    @measure({ name: "BrokenGameService.finalizeWithForceFinish", isAsync: true })
    public async finalizeWithForceFinish(merchant: MerchantInfo,
                                         tokenData: SeamlessAuthTokenData,
                                         finalizeGameRequest: FinalizeGameRequest): Promise<Balance> {

        const finalizeRoundData = this.makeResolveRoundIPMrequest(
            merchant,
            tokenData,
            finalizeGameRequest,
            EVENT_TYPE.FORCE_FINISH);

        return this.commitRoundResolve(merchant, finalizeRoundData);
    }

    private makeResolveRoundIPMrequest(merchant: MerchantInfo,
                                       authToken: SeamlessAuthTokenData,
                                       finalizeRequest: FinalizeGameRequest,
                                       finalizationEventType: string): any {
        const data = {
            merch_id: merchant.code,
            merch_pwd: merchant.params.password,

            cust_id: authToken.playerCode,
            game_code: authToken.gameCode,

            game_id: +finalizeRequest.roundId,
            round_id: finalizeRequest.roundPID || finalizeRequest.roundId,
            trx_id: this.paymentService.getTrxIdForPayload(merchant, finalizeRequest.transactionId),

            total_bet: finalizeRequest?.roundStatistics?.totalBet,
            total_win: finalizeRequest?.roundStatistics?.totalWin,
            currency_code: CurrencyConverter.toIPMCurrency(authToken.currency, merchant),

            game_status: "settled",
            event_type: finalizationEventType,

            timestamp: this.paymentService.getTimestamp(),
            is_finalization_payment: true
        } as any;

        if (finalizeRequest?.roundStatistics?.totalJpContribution) {
            data.total_jp_contribution = finalizeRequest.roundStatistics.totalJpContribution;
        }
        if (finalizeRequest?.roundStatistics?.totalJpWin) {
            data.total_jp_win = finalizeRequest.roundStatistics.totalJpWin;
            this.paymentService.decorateRequestWithJpWinStatistic(
                data,
                { jackpots: finalizeRequest.roundStatistics.jpStatistic, jackpotTypes: undefined });
        }

        if (finalizeRequest?.roundStatistics?.jpStatistic) {
            this.paymentService.decoratePaymentDataWithContributionStatistic(
                data,
                { jackpots: finalizeRequest.roundStatistics.jpStatistic, jackpotTypes: undefined });
        }

        if (finalizeRequest.roundStatistics?.smResult) {
            data.sm_result = finalizeRequest.roundStatistics.smResult;
        }

        return data;
    }

    public async loginGame(merchant: MerchantInfo,
                           gameTokenData: SeamlessAuthTokenData,
                           request: GameLoginRequest): Promise<void> {
        return undefined;
    }

    public async logoutGame(merchant: MerchantInfo,
                            gameTokenData: SeamlessAuthTokenData,
                            request: GameLogoutRequest): Promise<GameLogoutResponse> {
        const params = merchant.params;
        if (!params.gameLogoutOptions) {
            return { requireLogin: false };
        } else {
            const logoutPlayerRequest = {
                ...this.paymentService.getSensitiveData(merchant, gameTokenData),
                logout_id: request.logoutId,
                game_id: +request.roundId,
                round_id: request.roundPID || request.roundId,
                round_state: request.state,
                game_code: gameTokenData.gameCode,
                timestamp: this.paymentService.getTimestamp()
            };

            await this.service.doPost("/api/logout_player", logoutPlayerRequest, merchant);

            return { requireLogin: false };
        }
    }

    private async commitRoundResolve(merchant: MerchantInfo,
                                     finalizeRoundData): Promise<Balance> {

        const response = await this.service.doPost<SeamlessBalanceResponse>(
            "api/resolve_round",
            finalizeRoundData,
            merchant
        );

        return this.paymentService.balanceService.parseBalance(merchant, response);
    }
}

const logger = logging.logger("broken-game-service");

export function getBrokenGameService(apiService: SeamlessAPIService, regulation?): BrokenGameService {
    const service = new BrokenGameService(apiService, getPaymentService(apiService), logger);
    return applyRegulation(service, regulation);
}

import {
    Balance,
    BalanceRequestWithoutToken,
    Balances,
    DefaultMerchantBalanceService,
    MerchantInfo,
} from "@skywind-group/sw-wallet-adapter-core";
import { SeamlessAuthTokenData, SeamlessBalanceResponse } from "../../entities/seamless";
import { SeamlessAPIService } from "./seamlessAPIService";
import { logging, measures } from "@skywind-group/sw-utils";
import { MessageService, messageServiceContainer } from "./message";
import { MerchantInternalError } from "../../errors";
import { applyRegulation } from "../../regulations";
import { getPlatform } from "../../utils/getPlatform";
import measure = measures.measure;
import Logger = logging.Logger;

export class BalanceService extends DefaultMerchantBalanceService<SeamlessAuthTokenData> {
    constructor(public service: SeamlessAPIService,
                public messageService: MessageService,
                protected log: Logger) {
        super();
    }

    @measure({ name: "BalanceService.getBalances", isAsync: true })
    public async getBalances(merchant: MerchantInfo, gameToken: SeamlessAuthTokenData): Promise<Balances> {
        const balance = await this.getBalance(merchant, gameToken);

        return {
            [gameToken.currency]: balance
        };
    }

    public async getBalance(merchant: MerchantInfo, gameToken: SeamlessAuthTokenData): Promise<Balance> {
        const data = {
            game_code: gameToken.gameCode,
            merch_id: merchant.code,
            merch_pwd: merchant.params?.password,
            cust_id: gameToken.playerCode,
            cust_session_id: gameToken.ipmSessionId,
            platform: getPlatform(gameToken.deviceId)
        };

        const response = await this.service.doPost<SeamlessBalanceResponse>("api/get_balance", data, merchant);

        return this.parseBalance(merchant, response);
    }

    public parseBalance(merchant: MerchantInfo, response: SeamlessBalanceResponse): Balance {
        const { balance: main, free_bet_count: freeBetAmount, ...container } = response;

        const balance: Balance = {
            main: +main
        };

        if (freeBetAmount) {
            if (merchant.params?.isPromoInternal) {
                this.log.warn("Merchant free bets are processed in internal wallet and will be ignored.");
            } else {
                balance.freeBets = {
                    amount: +freeBetAmount
                };
            }
        }

        if (this.messageService.isMessageExists(container)) {
            balance.extraData = this.messageService.getExtraData(container);
        }

        return balance;
    }

    public getBalanceWithoutToken(merchant: MerchantInfo, request: BalanceRequestWithoutToken): Promise<Balance> {
        throw new MerchantInternalError("getBalanceWithoutToken do not supported");
    }
}

const logger = logging.logger("balance-service");

export function getBalanceService(apiService: SeamlessAPIService, regulation?): BalanceService {
    const service = new BalanceService(
        apiService,
        messageServiceContainer.get(),
        logger
    );
    return applyRegulation(service, regulation);
}

import {
    DefaultMerchantRegulationsService,
    MerchantInfo,
    PlayerRegulatoryActionRequest
} from "@skywind-group/sw-wallet-adapter-core";
import { SeamlessAuthTokenData } from "../../entities/seamless";
import { logging, measures } from "@skywind-group/sw-utils";
import { applyRegulation } from "../../regulations";
import {
    ReportCriticalFilesToMerchantRequest
} from "@skywind-group/sw-wallet-adapter-core/lib/skywind/definitions/regulation";
import measure = measures.measure;
import Logger = logging.Logger;

export class RegulationService extends DefaultMerchantRegulationsService<SeamlessAuthTokenData> {

    constructor(protected log: Logger) {
        super();
    }

    @measure({ name: "RegulationService.performRegulatoryAction", isAsync: true })
    public async performRegulatoryAction(merchant: MerchantInfo,
                                         gameToken: SeamlessAuthTokenData,
                                         request: PlayerRegulatoryActionRequest): Promise<any> {
        return null;
    }

    @measure({ name: "RegulationService.performRegulatoryAction", isAsync: true })
    public async reportCriticalFiles(merchant: MerchantInfo,
                                     request: ReportCriticalFilesToMerchantRequest): Promise<void> {
        return null;
    }
}

const logger = logging.logger("regulation-service");
export function getRegulationService(regulation): RegulationService {
    const service = new RegulationService(
        logger
    );
    return applyRegulation(service, regulation);
}

import { MERCHANT_REGULATION } from "@skywind-group/sw-wallet-adapter-core";
import { SeamlessBritishRegulation } from "./british";
import { SeamlessItalianRegulation } from "./italian";

export function applyRegulation(adapter, regulation: MERCHANT_REGULATION) {
    switch (regulation) {
        case MERCHANT_REGULATION.british:
            return new SeamlessBritishRegulation(adapter);
        case MERCHANT_REGULATION.italian:
            return new SeamlessItalianRegulation(adapter);
        default:
            return adapter;
    }
}

import {
    MerchantAdapterDecorator,
    MerchantGameTokenInfo,
    MerchantGameURLInfo,
    MerchantInfo,
    PlayerInfo,
} from "@skywind-group/sw-wallet-adapter-core";
import { SeamlessAuthTokenData, SeamlessGameInitRequest, SeamlessStartGameTokenData } from "../entities/seamless";

export interface IPMGameInitRequestWithAAMS extends SeamlessGameInitRequest {
    aamsParticipationCode?: string;
    aamsSessionId?: string;
}

/*
 Extension of IPM adapter for italian regulation.
 The main purpose of it is to check get game url params for AAMS data and
 add those to token so that it will be stored in history.
 */
export class SeamlessItalianRegulation
    extends MerchantAdapterDecorator<SeamlessGameInitRequest, SeamlessStartGameTokenData, SeamlessAuthTokenData> {

    public async createGameUrl(merchant: MerchantInfo,
                               gameCode: string,
                               providerCode: string,
                               providerGameCode: string,
                               initRequest: IPMGameInitRequestWithAAMS,
                               player?: PlayerInfo): Promise<MerchantGameURLInfo> {
        const result = await this.adapter.createGameUrl(merchant, gameCode, providerCode,
            providerGameCode, initRequest);

        if (initRequest.aamsSessionId || initRequest.aamsParticipationCode) {
            result.tokenData.regulatoryData = {
                aamsSessionCode: initRequest.aamsSessionId,
                ropCode: initRequest.aamsParticipationCode
            };
        }

        return result;
    }

    public async getGameTokenInfo(merchant: MerchantInfo,
                                  startGameTokenData: SeamlessStartGameTokenData,
                                  currency: string,
                                  transferEnabled: boolean): Promise<MerchantGameTokenInfo<SeamlessAuthTokenData>> {
        const result = await this.adapter.getGameTokenInfo(merchant, startGameTokenData, currency, transferEnabled);

        if (startGameTokenData.regulatoryData) {
            result.gameTokenData.regulatoryData = startGameTokenData.regulatoryData;
        }

        return result;
    }
}

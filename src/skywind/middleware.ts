import { Application, NextFunction, Request, Response } from "express";
import { checkSchema, validation<PERSON><PERSON><PERSON>, Schema } from "express-validator";
import { logging, measures } from "@skywind-group/sw-utils";
import Logger = logging.Logger;
import { ValidationError } from "./errors";
import { getSecuredObjectData } from "@skywind-group/sw-wallet-adapter-core";
import config from "./config";

export function afterSetupConfig(app: Application, logger: Logger): Application {
    app.use(createErrorHandler(logger));
    return app;
}

export function createErrorHandler(logger: Logger): (err: any, req: Request, res: Response, next: NextFunction) => any {
    return (err: any, req: Request, res: Response, next: NextFunction): any => {

        measures.measureProvider.saveError(err);

        if (err) {
            logger.error(err, "Seamless integration Error");
            sendError(err as any, res);
        }
    };
}

export function createRequestLogger(logger: Logger): (req: Request, res: Response, next: NextFunction) => any {
    return (req: Request, res: Response, next: NextFunction): any => {
        if (req.originalUrl.endsWith("version") || req.originalUrl.endsWith("health")) {
            return next();
        }

        const data = {
            tag: req.protocol,
            method: req.method,
            url: req.originalUrl,
            query: req.query,
            ip: req.ip,
            userAgent: req.headers["user-agent"],
            body: req.body && getSecuredObjectData(req.body, config.logParams.secureKeys)
        };

        logger.debug(data, "Http request");
        next();
    };
}

function sendError(error: any, response: Response) {
    response.status(error.responseStatus || 500).json({
        code: error.code,
        message: error.message,
        ...error
    });
}

export function setUpValidatorAndHeaders(app: Application): Application {
    app.use("/*", addHeaderCORS);

    return app;
}

const DEFAULT_MAX_AGE_OPTIONS = (30 * 24 * 60 * 60).toString();

function addHeaderCORS(req: Request, res: Response, next: NextFunction) {
    if (res.headersSent) {
        next();
        return;
    }
    /* TODO:
     * we currently use "*" (all http origins),
     * it must be replaced with the origins white list, when implemented
     */
    res.setHeader("Access-Control-Allow-Origin", "*");
    if (req.method === "OPTIONS") {
        /* TODO:
         * list of headers must be defined
         */
        if (req.header("Access-Control-Request-Headers")) {
            res.setHeader("Access-Control-Allow-Headers", req.header("Access-Control-Request-Headers"));
        }
        res.setHeader("Access-Control-Max-Age", DEFAULT_MAX_AGE_OPTIONS);

        res.status(204).end();
    } else {
        next();
    }
}
export const validate = (schema: Schema) => {
    const validations = checkSchema(schema);
    return async (req: Request, res: Response, next: NextFunction) => {
        await Promise.all(validations.map((validation) => validation.run(req)));

        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const errorMessages = errors.array().map((e: any) => `${e.path} - ${e.msg.toLowerCase()}`).join(", ");
            return next(new ValidationError(errorMessages));
        }

        next();
    };
};

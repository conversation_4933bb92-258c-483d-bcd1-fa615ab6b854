import {
    CannotCompletePayment,
    escapeSomeHtmlChars,
    ExtraData,
    MerchantInfo,
    PaymentRequest,
    RequireRefundBetError,
    SWError
} from "@skywind-group/sw-wallet-adapter-core";
import { extraDataStoreLazy } from "./utils/extraDataStore";
import { errors } from "@skywind-group/sw-utils";
import ERROR_LEVEL = errors.ERROR_LEVEL;
import { SeamlessAuthTokenData } from "./entities/seamless";

export class ValidationError extends SWError {
    constructor(message: string = "") {
        super(400, 40, message);

    }
}

export class PlayerNotFoundError extends SWError {
    constructor() {
        super(404, 102, "Player not found");
    }
}

export class MerchantMisconfiguration extends SWError {
    constructor() {
        super(500, 505, "Merchant should have url and password in params");
    }
}

export class MerchantInternalError extends SWError {
    constructor(reason?: string, responseStatus: number = 500) {
        super(responseStatus, 506, "Merchant internal error" + (reason ? `: ${escapeSomeHtmlChars(reason)}` : ""));
        this.data.reason = reason || "";
    }
}

export class MerchantIntegrationGeneralError extends SWError {
    constructor(reason?: string) {
        super(500, 506, "Merchant general error" + (reason ? `: ${escapeSomeHtmlChars(reason)}` : ""));
        this.data.reason = reason || "";
    }
}

export class SeamlessError extends SWError {
    constructor(errorCode: number, errorMessage: string) {
        super(500, 507, `Error during integration with merchant: code=${escapeSomeHtmlChars("" + errorCode)}`);
        this.providerDetails = {
            errorCode: errorCode,
            errorMsg: errorMessage
        };
    }
}

export class GameTokenExpired extends SWError {
    constructor() {
        super(400, 323, "Game token expired", ERROR_LEVEL.INFO);
    }
}

export class InsufficientBalanceError extends SWError {
    constructor() {
        super(400, 91, "Player does not have sufficient balance to perform an operation");
    }
}

export class InsufficientFreebet extends SWError {
    constructor() {
        super(400, 685, "Insufficient free bets balance");
    }
}

export class InvalidFreebet extends SWError {
    constructor() {
        super(400, 686, "Invalid free bet");
    }
}

export class TransactionNotFound extends SWError {
    constructor() {
        super(404, 600, "Transaction not found");
    }
}

export class PlayerIsSuspended extends SWError {
    constructor() {
        super(400, 712, "Player is suspended");
    }
}

export class ExceedBetLimit extends SWError {
    constructor() {
        super(403, 752, "Bet limit was exceeded");
    }
}

export class PlayerOnTimeoutError extends SWError {
    constructor(public timeoutTill?: Date) {
        super(403, 1500, "Can't execute operation. Player is on timeout.");
    }
}

export class PlayerIsSelfExcludedError extends SWError {

    private selfExclusionTIll?: Date;

    constructor(message?: string) {
        super(403, 1501, message ? message : "Can't execute operation. Player is self-excluded.");
    }

    public setSelfExclusionDate(date: Date): PlayerIsSelfExcludedError {
        this.selfExclusionTIll = date;
        return this;
    }
}

export class RGPlayerDepositLimitReachedError extends SWError {
    constructor() {
        super(403, 1502, "Can't execute operation. Player has reached his deposit limit.");
    }
}

// generic error for RG session limit time exceeding
export class RGSessionTimeLimitReachedError extends SWError {
    constructor() {
        super(403, 1503, "You have reached your session time limit.");
    }
}

export class RGDailyTimeLimitReachedError extends SWError {
    constructor() {
        super(403, 1504,
            "You have reached your daily time limit you previously set. Please come back and play again tomorrow.");
    }
}

export class RGRealityCheckError extends SWError {
    constructor(message?: string, extraData?: ExtraData) {
        super(
            403,
            1505,
            message ? message : "We would like to notify you that " +
                "you've already played for your reality check interval.",
            undefined,
            extraData
        );
    }
}

export class RepeatingSelfExclusionError extends SWError {
    constructor() {
        super(403, 1506, "Can't execute operation. Player is permanently blocked.");
    }
}

export class RGDailyLossLimitExceedWarningError extends SWError {
    constructor(message: string = "The bet amount you selected exceeds your loss limit for today.",
                extraData?: ExtraData) {
        super(403, 1507, message, undefined, extraData);
    }
}

export class RGDailyLossLimitReachedError extends SWError {
    constructor(message: string = "You've reached your loss limit for today.", extraData?: ExtraData) {
        super(403, 1508, message, undefined, extraData);
    }
}

export class RGWeeklyLossLimitExceedWarningError extends SWError {
    constructor(message: string = "The bet amount you selected exceeds your loss limit for this week.",
                extraData?: ExtraData) {
        super(403, 1509, message, undefined, extraData);
    }
}

export class RGWeeklyLossLimitReachedError extends SWError {
    constructor(message: string = "You've reached your loss limit for this week.", extraData?: ExtraData) {
        super(403, 1510, message, undefined, extraData);
    }
}

export class RGMonthlyLossLimitExceedWarningError extends SWError {
    constructor(message: string = "The bet amount you selected exceeds your loss limit for this month.",
                extraData?: ExtraData) {
        super(403, 1511, message, undefined, extraData);
    }
}

export class RGMonthlyLossLimitReachedError extends SWError {
    constructor(message: string = "You've reached your loss limit for this month.", extraData?: ExtraData) {
        super(403, 1512, message, undefined, extraData);
    }
}

export class RGSessionLossLimitReachedError extends SWError {
    constructor(message: string = "You've reached your session loss limit.", extraData?: ExtraData) {
        super(403, 1513, message, undefined, extraData);
    }
}

export class RGRegulatoryCustomError extends SWError {
    constructor(message: string = "Player has exceeded regulatory limits.", extraData?: ExtraData) {
        super(403, 1514, message, undefined, extraData);
    }
}

export class RGSessionBetLimitReachedError extends SWError {
    constructor(message: string = "You've reached your session bet limit.", extraData?: ExtraData) {
        super(403, 1515, message, undefined, extraData);
    }
}

export class RGDailyBetLimitReachedError extends SWError {
    constructor(message: string = "You've reached your bet limit for today.", extraData?: ExtraData) {
        super(403, 1516, message, undefined, extraData);
    }
}

export class RGWeeklyBetLimitReachedError extends SWError {
    constructor(message: string = "You've reached your bet limit for this week.", extraData?: ExtraData) {
        super(403, 1517, message, undefined, extraData);
    }
}

export class RGMonthlyBetLimitReachedError extends SWError {
    constructor(message: string = "You've reached your bet limit for this month.", extraData?: ExtraData) {
        super(403, 1518, message, undefined, extraData);
    }
}

export class RGWithdrawLimitReachedError extends SWError {
    constructor(message: string = "You've reached your withdraw limit.") {
        super(403, 1519, message);
    }
}

export class RGMandatoryLimitMissingError extends SWError {
    constructor(message: string = "Mandatory limit missing.", extraData?: ExtraData) {
        super(403, 1520, message, undefined, extraData);
    }
}

export class RGSessionGameGroupLossLimitReachedError extends SWError {
    constructor(message: string = "You've reached your session game group loss limit.", extraData?: ExtraData) {
        super(403, 1521, message, undefined, extraData);
    }
}

export class RGDailyGameGroupLossLimitReachedError extends SWError {
    constructor(message: string = "You've reached your game group loss limit for today.", extraData?: ExtraData) {
        super(403, 1522, message, undefined, extraData);
    }
}

export class RGWeeklyGameGroupLossLimitReachedError extends SWError {
    constructor(message: string = "You've reached your game group loss limit for this week.", extraData?: ExtraData) {
        super(403, 1523, message, undefined, extraData);
    }
}

export class RGMonthlyGameGroupLossLimitReachedError extends SWError {
    constructor(message: string = "You've reached your game group loss limit for this month.", extraData?: ExtraData) {
        super(403, 1524, message, undefined, extraData);
    }
}

export class RGMaxBetExceededForBonusMoneyError extends SWError {
    constructor(message: string = "You have exceeded max bet for bonus funds.", extraData?: ExtraData) {
        super(403, 1525, message, undefined, extraData);
    }
}

export class RGDailyTimeLimitExceeded extends SWError {
    constructor(message: string = "You have exceeded the daily time limit.", extraData?: ExtraData) {
        super(403, 1526, message, undefined, extraData);
    }
}

export class RGWeeklyTimeLimitExceeded extends SWError {
    constructor(message: string = "You have exceeded the weekly time limit.", extraData?: ExtraData) {
        super(403, 1527, message, undefined, extraData);
    }
}

export class RGMonthlyTimeLimitExceeded extends SWError {
    constructor(message: string = "You have exceeded the monthly time limit.", extraData?: ExtraData) {
        super(403, 1528, message, undefined, extraData);
    }
}

export class RGRealityCheckWithTimeError extends RGRealityCheckError {
    constructor(realityCheckInterval?: number, message?: string, extraData?: ExtraData) {
        super(
            message ? message :
            `Your session has now exceeded ${realityCheckInterval} minutes.` +
                "We would like to notify you that you've already played for your reality check interval."
            ,
            extraData
        );
    }
}

export class RGRealityCheckLimitReachedError extends SWError {
    constructor(message: string = "You have reached your limit.", extraData?: ExtraData) {
        super(403, 1530, message, undefined, extraData);
    }
}

export class RGRealityCheckLossLimitReachedError extends SWError {
    constructor(message: string = "You have reached your loss limit.", extraData?: ExtraData) {
        super(403, 1531, message, undefined, extraData);
    }
}

export class BetRejectedError extends SWError {
    constructor(message: string = "Your bet was not accepted and your account has not been charged.") {
        super(403, 1601, message);
    }
}

export class ChangeBetError extends SWError {
    constructor(message: string = "Insufficient balance. Please try to change your bet.") {
        super(403, 1602, message);
    }
}

export class TimeoutError extends SWError {
    constructor(message: string = "Timeout error") {
        super(500, 2013, message);
    }
}

export class RollbackTransactionError extends SWError {
    constructor(message: string = "Rollback transaction") {
        super(400, 2014, message);
    }
}

export class GeneralError extends SWError {
    constructor(message: string = "General error") {
        super(500, 2015, message);
    }
}

export class RequestHashCodeError extends SWError {
    constructor() {
        super(403, 5, "Invalid hash code of the request");
    }
}

export enum SEAMLESS_ERROR_CODE {
    SUCCESS = 0,
    DUPLICATE_TRX_ERROR = 1,
    INVALID_REQUEST_HASH = 5,
    GENERAL_ERROR = -1,
    PLAYER_NOT_FOUND = -2,
    GAME_TOKEN_EXPIRED = -3,
    INSUFFICIENT_BALANCE_ERROR = -4,
    INSUFFICIENT_FREEBET = -5,
    INVALID_FREEBET = -6,
    TRANSACTION_NOT_FOUND = -7,

    PLAYER_IS_SUSPENDED = -301,
    EXCEED_BET_LIMIT = -302,

    PLAYER_ON_TIMEOUT = -1500,
    PLAYER_IS_SELF_EXCLUDED = -1501,
    RG_PLAYER_DEPOSIT_LIMIT_REACHED = -1502,
    RG_SESSION_TIME_LIMIT_REACHED = -1503,
    RG_DAILY_TIME_LIMIT_REACHED = -1504,
    RG_REALITY_CHECK = -1505,
    REPEATING_SELF_EXCLUSION = -1506,
    RG_DAILY_LOSS_LIMIT_EXCEED_WARNING = -1507,
    RG_DAILY_LOSS_LIMIT_REACHED = -1508,
    RG_WEEKLY_LOSS_LIMIT_EXCEED_WARNING = -1509,
    RG_WEEKLY_LOSS_LIMIT_REACHED = -1510,
    RG_MONTHLY_LOSS_LIMIT_EXCEED_WARNING = -1511,
    RG_MONTHLY_LOSS_LIMIT_REACHED = -1512,
    RG_SESSION_LOSS_LIMIT_REACHED = -1513,
    RG_REGULATORY_CUSTOM = -1514,
    RG_SESSION_BET_LIMIT_REACHED = -1515,
    RG_DAILY_BET_LIMIT_REACHED = -1516,
    RG_WEEKLY_BET_LIMIT_REACHED = -1517,
    RG_MONTHLY_BET_LIMIT_REACHED = -1518,
    RG_WITHDRAW_LIMIT_REACHED = -1519,
    RG_MANDATORY_LIMIT_MISSING = -1520,
    RG_SESSION_GAME_GROUP_LOSS_LIMIT_REACHED = -1521,
    RG_DAILY_GAME_GROUP_LOSS_LIMIT_REACHED = -1522,
    RG_WEEKLY_GAME_GROUP_LOSS_LIMIT_REACHED = -1523,
    RG_MONTHLY_GAME_GROUP_LOSS_LIMIT = -1524,
    RG_MAX_BET_EXCEEDED_FOR_BONUS_MONEY = -1525,
    RG_DAILY_TIME_LIMIT_EXCEEDED = -1526,
    RG_WEEKLY_TIME_LIMIT_EXCEEDED = -1527,
    RG_MONTHLY_TIME_LIMIT_EXCEEDED = -1528,
    RG_REALITY_CHECK_WITH_TIME = -1529,
    RG_REALITY_CHECK_LIMIT = -1530,
    RG_REALITY_CHECK_GENERIC_LOSS_LIMIT_REACHED = -1531,

    // custom behaviour errors
    BET_REJECTED_ERROR = -1601, // stops reels on default stop positions and displays a message to the player
    /**
     * stops reels on default stop positions and informs the players that
     * they don't have enough balance and should try to change their bet amount
     */
    CHANGE_BET_ERROR = -1602
}

const SUCCESSFUL_RESPONSE_CODES = [SEAMLESS_ERROR_CODE.DUPLICATE_TRX_ERROR, SEAMLESS_ERROR_CODE.SUCCESS];

export function getSWError(errorCode: number, errorMsg: string = "", merchant: MerchantInfo, rci?: number): SWError {
    switch (errorCode) {
        case SEAMLESS_ERROR_CODE.GENERAL_ERROR:
            return new MerchantIntegrationGeneralError(errorMsg);
        case SEAMLESS_ERROR_CODE.PLAYER_NOT_FOUND:
            return new PlayerNotFoundError();
        case SEAMLESS_ERROR_CODE.GAME_TOKEN_EXPIRED:
            return new GameTokenExpired();
        case SEAMLESS_ERROR_CODE.INSUFFICIENT_BALANCE_ERROR:
            return merchant.params.changeBetOnInsufficientBalance ?
                   new ChangeBetError() :
                   new InsufficientBalanceError();
        case SEAMLESS_ERROR_CODE.INSUFFICIENT_FREEBET:
            return new InsufficientFreebet();
        case SEAMLESS_ERROR_CODE.INVALID_FREEBET:
            return new InvalidFreebet();
        case SEAMLESS_ERROR_CODE.TRANSACTION_NOT_FOUND:
            return new TransactionNotFound();
        case SEAMLESS_ERROR_CODE.PLAYER_IS_SUSPENDED:
            return new PlayerIsSuspended();
        case SEAMLESS_ERROR_CODE.EXCEED_BET_LIMIT:
            return new ExceedBetLimit();
        case SEAMLESS_ERROR_CODE.INVALID_REQUEST_HASH:
            return new RequestHashCodeError();

        // responsible gaming errors
        case SEAMLESS_ERROR_CODE.PLAYER_ON_TIMEOUT:
            return new PlayerOnTimeoutError();
        case SEAMLESS_ERROR_CODE.PLAYER_IS_SELF_EXCLUDED:
            return new PlayerIsSelfExcludedError();
        case SEAMLESS_ERROR_CODE.RG_PLAYER_DEPOSIT_LIMIT_REACHED:
            return new RGPlayerDepositLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_SESSION_TIME_LIMIT_REACHED:
            return new RGSessionTimeLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_DAILY_TIME_LIMIT_REACHED:
            return new RGDailyTimeLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_REALITY_CHECK:
            return new RGRealityCheckError(
                escapeSomeHtmlChars(errorMsg),
                getExtraDataForRealityCheck(merchant)
            );
        case SEAMLESS_ERROR_CODE.REPEATING_SELF_EXCLUSION:
            return new RepeatingSelfExclusionError();
        case SEAMLESS_ERROR_CODE.RG_DAILY_LOSS_LIMIT_EXCEED_WARNING:
            return new RGDailyLossLimitExceedWarningError();
        case SEAMLESS_ERROR_CODE.RG_DAILY_LOSS_LIMIT_REACHED:
            return new RGDailyLossLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_WEEKLY_LOSS_LIMIT_EXCEED_WARNING:
            return new RGWeeklyLossLimitExceedWarningError();
        case SEAMLESS_ERROR_CODE.RG_WEEKLY_LOSS_LIMIT_REACHED:
            return new RGWeeklyLossLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_MONTHLY_LOSS_LIMIT_EXCEED_WARNING:
            return new RGMonthlyLossLimitExceedWarningError();
        case SEAMLESS_ERROR_CODE.RG_MONTHLY_LOSS_LIMIT_REACHED:
            return new RGMonthlyLossLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_SESSION_LOSS_LIMIT_REACHED:
            return new RGSessionLossLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_REGULATORY_CUSTOM:
            return getRegulatoryCustomError(errorMsg);
        case SEAMLESS_ERROR_CODE.RG_SESSION_BET_LIMIT_REACHED:
            return new RGSessionBetLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_DAILY_BET_LIMIT_REACHED:
            return new RGDailyBetLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_WEEKLY_BET_LIMIT_REACHED:
            return new RGWeeklyBetLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_MONTHLY_BET_LIMIT_REACHED:
            return new RGMonthlyBetLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_WITHDRAW_LIMIT_REACHED:
            return new RGWithdrawLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_MANDATORY_LIMIT_MISSING:
            return new RGMandatoryLimitMissingError();
        case SEAMLESS_ERROR_CODE.RG_SESSION_GAME_GROUP_LOSS_LIMIT_REACHED:
            return new RGSessionGameGroupLossLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_DAILY_GAME_GROUP_LOSS_LIMIT_REACHED:
            return new RGDailyGameGroupLossLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_WEEKLY_GAME_GROUP_LOSS_LIMIT_REACHED:
            return new RGWeeklyGameGroupLossLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_MONTHLY_GAME_GROUP_LOSS_LIMIT:
            return new RGMonthlyGameGroupLossLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_MAX_BET_EXCEEDED_FOR_BONUS_MONEY:
            return new RGMaxBetExceededForBonusMoneyError();
        case SEAMLESS_ERROR_CODE.RG_DAILY_TIME_LIMIT_EXCEEDED:
            return new RGDailyTimeLimitExceeded();
        case SEAMLESS_ERROR_CODE.RG_WEEKLY_TIME_LIMIT_EXCEEDED:
            return new RGWeeklyTimeLimitExceeded();
        case SEAMLESS_ERROR_CODE.RG_MONTHLY_TIME_LIMIT_EXCEEDED:
            return new RGMonthlyTimeLimitExceeded();
        case SEAMLESS_ERROR_CODE.RG_REALITY_CHECK_WITH_TIME:
            return new RGRealityCheckWithTimeError(rci);
        case SEAMLESS_ERROR_CODE.RG_REALITY_CHECK_LIMIT:
            return new RGRealityCheckLimitReachedError();
        case SEAMLESS_ERROR_CODE.RG_REALITY_CHECK_GENERIC_LOSS_LIMIT_REACHED:
            return new RGRealityCheckLossLimitReachedError();
        // end of responsible gaming errors
        case SEAMLESS_ERROR_CODE.BET_REJECTED_ERROR:
            return new BetRejectedError();
        case SEAMLESS_ERROR_CODE.CHANGE_BET_ERROR:
            return new ChangeBetError();
        default:
            return new SeamlessError(errorCode, errorMsg);
    }
}

function getExtraDataForRealityCheck(merchant: MerchantInfo): ExtraData {
    if (merchant?.params?.ignoreRealityCheck) {
        return extraDataStoreLazy.get().getIgnorableRealityCheckExtraData();
    }
    if (merchant?.params?.showRealityCheckGameHistory) {
        return extraDataStoreLazy.get().getRealityCheckExtraData(true);
    }
    return extraDataStoreLazy.get().getRealityCheckExtraData();
}

function getRegulatoryCustomError(errorMsg: string): SWError {
    const regulatoryCustomError = new RGRegulatoryCustomError(escapeSomeHtmlChars(errorMsg),
        extraDataStoreLazy.get().getRegulatoryCustomErrorExtraData());
    if (errorMsg && typeof errorMsg === "string") {
        regulatoryCustomError.dontTranslate();
    }
    return regulatoryCustomError;
}

export function isSuccessResponse(errorCode: SEAMLESS_ERROR_CODE): boolean {
    return SUCCESSFUL_RESPONSE_CODES.includes(errorCode);
}

export const rollbackCondition = (err: SWError): boolean =>
    err instanceof MerchantInternalError ||
    err instanceof TimeoutError ||
    err instanceof GeneralError ||
    err instanceof SeamlessError ||
    err instanceof MerchantIntegrationGeneralError;

export const retryCondition = (tokenData: SeamlessAuthTokenData) => (err: SWError) =>
    !tokenData.isLiveGame &&
    !tokenData.forbidOnlineRetries &&
    rollbackCondition(err);
export const retryBetCondition = (isNeedRetry: boolean) => (err: SWError) => rollbackCondition(err) && isNeedRetry;
export const wrapRollbackError = (err: SWError) =>
    rollbackCondition(err) || err instanceof GameTokenExpired ?
    new RequireRefundBetError(`Require to refund bet - ${err.message}`) : new RollbackTransactionError(err.message);

export const wrapWinError = (isOfflineRetry: boolean = false) =>
    (err: SWError) => {
        if ((err instanceof GameTokenExpired || err instanceof TransactionNotFound) && isOfflineRetry) {
            return new CannotCompletePayment();
        }

        if (err instanceof ValidationError) {
            return new MerchantInternalError(err.message, err.responseStatus);
        }

        return err;
    };

/**
 * upgrade SWError - add extra data, error mapping e.t.c
 * Note, that error that in a callback function is expected to be thrown by SeamlessAPIService.process() method.
 */
export const wrapBetError = (
    isNeedRetry: boolean,
    tokenData: SeamlessAuthTokenData,
    paymentRequest: PaymentRequest,
    isRefundOnBadRequestRequired?: boolean
) =>
    (err: SWError) => {
        if (err instanceof GameTokenExpired && paymentRequest.offlineRetry) {
            return new CannotCompletePayment(err.message);
        }
        if (err instanceof ExceedBetLimit) {
            const hasLobby = tokenData?.hasLobby || false;
            const extraDataStore = extraDataStoreLazy.get();
            err.setExtraData(extraDataStore.getBetLimitExceedExtraData(err, hasLobby));
        }

        if (rollbackCondition(err) && !isNeedRetry && (!paymentRequest.offlineRetry || paymentRequest.bet)) {
            return new RequireRefundBetError(`Require to refund bet - ${err.message}`);
        }

        if (err instanceof ValidationError) {
            if (isRefundOnBadRequestRequired && !isNeedRetry && (!paymentRequest.offlineRetry || paymentRequest.bet)) {
                return new RequireRefundBetError(`Require to refund bet - ${err.message}`);
            } else if (isNeedRetry) {
                return new MerchantInternalError(err.message, err.responseStatus);
            }
        }

        return err;
    };

// this should be the first line
import { measures, logging } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import config from "./config";

logging.setUpOutput({ type: config.loggingOutput, logLevel: config.logLevel });
logging.setRootLogger("seamless");

import { startServer } from "./server";
import { startServer as startInternalServer } from "./serverInternal";
import { setupProcessEvents } from "./utils/setupProcessEvents";
import { createContainer } from "./wallet/bootstrap";

// Start server
(async () => {
    setupProcessEvents(logging.logger("process"));

    const container = createContainer();
    await startServer("Seamless Wallet Adapter", container);
    await startInternalServer();
})();

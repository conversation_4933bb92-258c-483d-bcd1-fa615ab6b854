import {
    ExtraMessageImpl,
    MrchExtraData,
    MrchExtraDataImpl,
    PlayerActionServerCallImpl,
    PlayerRegulatoryActionsAtServer,
    PopupButtonGameActions,
    PopupButtonImpl,
    SWError
} from "@skywind-group/sw-wallet-adapter-core";
import { lazy } from "@skywind-group/sw-utils";

export class SeamlessExtraDataStore {

    private realityCheckExtraData: MrchExtraData;
    private ignorableRealityCheckExtraData: MrchExtraData;
    private regulatoryCustomErrorExtraData: MrchExtraData;

    public getRealityCheckExtraData(showRealityCheckGameHistory = false): MrchExtraData {
        this.realityCheckExtraData = this.initRealityCheckExtraData(showRealityCheckGameHistory);
        return this.realityCheckExtraData;
    }

    // Message with type = 2 (show error in console only).
    // Its to be used in cases when operator wants to show his own message to player.
    public getIgnorableRealityCheckExtraData(): MrchExtraData {
        if (!this.ignorableRealityCheckExtraData) {
            this.ignorableRealityCheckExtraData = this.initRealityCheckExtraData();
            // tslint:disable-next-line:max-line-length
            // https://confluence.skywindgroup.com/display/SWS/Client+Protocol+of+Error+Messages#ClientProtocolofErrorMessages-Ignorablemessage
            this.ignorableRealityCheckExtraData.errorVisibility = 2; // show error in console only
        }
        return this.ignorableRealityCheckExtraData;
    }

    public getRegulatoryCustomErrorExtraData(): MrchExtraData {
        if (!this.regulatoryCustomErrorExtraData) {
            this.regulatoryCustomErrorExtraData = this.initRegulatoryCustomErrorExtraData();
        }
        return this.regulatoryCustomErrorExtraData;
    }

    public getBetLimitExceedExtraData(error: SWError, hasLobby: boolean): MrchExtraData {
        const action = hasLobby ? PopupButtonGameActions.lobby : PopupButtonGameActions.refresh;
        const button = PopupButtonImpl
            .create()
            .setLabel("Ok")
            .setGameAction(action);
        const extraMessage = ExtraMessageImpl.create()
            .addButton(button);
        return MrchExtraDataImpl.create()
            .addExtraMessage(extraMessage);
    }

    private initRealityCheckExtraData(showRealityCheckGameHistory = false): MrchExtraData {
        const okButtonServerCall = PlayerActionServerCallImpl.create()
            .setRegulatoryAction(PlayerRegulatoryActionsAtServer.resetRealityCheck);

        const stopButtonServerCall = PlayerActionServerCallImpl.create()
            .setRegulatoryAction(PlayerRegulatoryActionsAtServer.closeSession);

        const okButton = PopupButtonImpl.create()
            .setLabel("Keep Playing")
            .setGameAction(PopupButtonGameActions.continue)
            .setServerCall(okButtonServerCall)
            .setTranslate(true);

        const stopButton = PopupButtonImpl.create()
            .setLabel("Quit")
            .setGameAction(PopupButtonGameActions.lobby)
            .setServerCall(stopButtonServerCall)
            .setTranslate(true);

        const buttons = [okButton, stopButton];

        if (showRealityCheckGameHistory) {
            const gameHistoryButton = PopupButtonImpl.create()
                .setLabel("Game History")
                .setGameAction(PopupButtonGameActions.gameHistory)
                .setTranslate(true);
            buttons.push(gameHistoryButton);
        }
        const realityCheckExtraMessage = ExtraMessageImpl.create()
            .setButtons(buttons)
            .setTranslate(true);

        return MrchExtraDataImpl.create().addExtraMessage(realityCheckExtraMessage);
    }

    private initRegulatoryCustomErrorExtraData(): MrchExtraData {
        const okButton = PopupButtonImpl.create()
            .setLabel("OK")
            .setGameAction(PopupButtonGameActions.refresh)
            .setTranslate(true);
        const extraMessage = ExtraMessageImpl.create().setButtons([okButton]);
        return MrchExtraDataImpl
            .create()
            .setUseServerMessage(true)
            .addExtraMessage(extraMessage);
    }
}

export const extraDataStoreLazy = lazy(() => new SeamlessExtraDataStore());

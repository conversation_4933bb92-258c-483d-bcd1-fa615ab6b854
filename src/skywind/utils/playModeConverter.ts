import { MerchantInfo, PlayMode } from "@skywind-group/sw-wallet-adapter-core";

export function playModeConverter(playMode: PlayMode | undefined,
                                  currency: string | undefined,
                                  { params: { playMoneyCurrencies } }: MerchantInfo): PlayMode {
    if (currency && Array.isArray(playMoneyCurrencies) && playMoneyCurrencies.length) {
        if (playMoneyCurrencies.includes(currency.toUpperCase())) {
            return PlayMode.PLAY_MONEY;
        }
    }
    return playMode;
}

import { MerchantInfo } from "@skywind-group/sw-wallet-adapter-core";

// IDS and VNS currencies which are stored in system already in thousands because of their huge exchange rate
const fromIPMCurrencyMap: Record<string, string> = {
    IDR: "IDS",
    VND: "VNS"
};
const toIPMCurrencyMap = invert(fromIPMCurrencyMap);

function invert(record: Record<string, string>): Record<string, string> {
    return Object.entries(record).reduce<Record<string, string>>((result, [key, value]) => ({
        ...result,
        [value]: key
    }), {});
}

export class CurrencyConverter {

    public static fromIPMCurrency(currencyCode: string, merchant: MerchantInfo): string {
        let newCurrencyCode: string;
        if (merchant.params.shortCurrencyEnabled) {
            newCurrencyCode = fromIPMCurrencyMap[currencyCode];
        }
        if (newCurrencyCode) {
            return newCurrencyCode;
        }
        return merchant.params?.currencies?.[currencyCode] || currencyCode;
    }

    public static toIPMCurrency(currencyCode: string, merchant: MerchantInfo) {
        let newCurrencyCode: string;
        if (merchant.params.shortCurrencyEnabled) {
            newCurrencyCode = toIPMCurrencyMap[currencyCode];
        }
        if (newCurrencyCode) {
            return newCurrencyCode;
        }
        return invert(merchant.params?.currencies || {})[currencyCode] || currencyCode;
    }
}

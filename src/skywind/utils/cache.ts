import * as NodeCache from "node-cache";
import config from "../config";

class Cache {
    private cache: NodeCache;
    private static instance: Cache = null;
    private constructor(ttlSeconds: number, checkPeriodSeconds: number) {
        this.cache = new NodeCache({ stdTTL: ttlSeconds, checkperiod: checkPeriodSeconds, useClones: false });
    }

    public static getInstance(): Cache {
        if (!this.instance) {
            this.instance = new Cache(config.cache.ttl, config.cache.checkPeriod);
        }
        return this.instance;
    }

    public set(key: string, value: any): void {
        this.cache.set(key, value);
    }
    public get(key: string): object {
        return this.cache.get(key);
    }
    public del(key: string): number {
        return this.cache.del(key);
    }
}

export default Cache;

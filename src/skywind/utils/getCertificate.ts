import { readFile } from "fs";
import { promisify } from "util";
import { SeamlessCertificate, SeamlessCertificateCredentials } from "../entities/seamless";
import Cache from "./cache";
import { logging } from "@skywind-group/sw-utils";
import logger = logging.logger;
import { getSecuredObjectData, MerchantInfo } from "@skywind-group/sw-wallet-adapter-core";
import config from "../config";

const cache = Cache.getInstance();
const readFileAsync = promisify(readFile);
const log = logger("load-certificate");

interface RawPOPCertAndKey {
    cert: Buffer;
    key: Buffer;
}

/**
 * Certificate service that is intended to get ssl certificate data that will be further used in requests to wallet.
 * Logic behind this:
 * 1) Certificate data for IPM merchants can be stored only as files within a container.
 * 2) Files must be stored in the same way it was made in pop wallet - .cert, .key and .json files must have a name of
 * appropriate merchantCode, for example, /ipmMerch1.json, /ipmMerch1.cert, /ipmMerch1.key.
 * Also, with SEAMLESS_CERT_DIRECTORY_PATH env var Devops can set base path to a folder with cert files.
 * example: /SEAMLESS_CERT_DIRECTORY_PATH/ipmMerch1.cert
 * 3) Unlike in pop and gvc wallets, cert data is not read from config.ts, env variable files and merchant.params.
 * 4) In order to use cert data in requests, merchant.params.certSettings.useCert must be set true. When its true,
 * SeamlessAPIService will try to get cert data from cache or read from files in container.
 */
export class CertificateService {

    private static certificateServiceInstance;

    public static getInstance() {
        if (!this.certificateServiceInstance) {
            this.certificateServiceInstance = new CertificateService();
        }
        return this.certificateServiceInstance;
    }

    // ca and password are intended to be stored in a separate JSON file, such like '/merchantCode.json'
    public async readPasswordAndCAfromJSON(directoryPath: string,
                                           merchantCode: string, logErrors: boolean = false): Promise<Buffer> {
        let rawCredentials: Buffer;
        try {
            rawCredentials = await readFileAsync(`${directoryPath}/${merchantCode}.json`);
        } catch (err) {
            if (logErrors) {
                log.debug(err, "Error when trying to read password and ca from file.");
            }
        }
        return rawCredentials;
    }

    private parsePasswordAndCA(rawCredentials: string, logErrors: boolean = false): SeamlessCertificateCredentials {
        let parsedCredentials: SeamlessCertificateCredentials;
        try {
            parsedCredentials = JSON.parse(rawCredentials.toString());
        } catch (err) {
            if (logErrors) {
                log.debug(err, "Error when trying to parse password and ca.");
            }
        }
        return parsedCredentials;
    }

    private async getPasswordAndCA(directoryPath: string,
                                   merchantCode: string,
                                   logErrors: boolean = false): Promise<SeamlessCertificateCredentials> {
        const rawCredentials = await this.readPasswordAndCAfromJSON(directoryPath, merchantCode, logErrors);
        if (!rawCredentials) {
            return {};
        }

        const parsedCredentials = this.parsePasswordAndCA(rawCredentials.toString());
        if (!parsedCredentials) {
            return {};
        }

        const result: SeamlessCertificateCredentials = {};
        if (parsedCredentials.password) {
            result.password = parsedCredentials.password;
        }
        if (parsedCredentials.ca) {
            result.ca = parsedCredentials.ca;
        }
        return result;
    }

    public async readCertAndKeyFiles(directoryPath: string,
                                     merchantCode: string,
                                     logErrors: boolean = false): Promise<RawPOPCertAndKey> {
        let cert: Buffer;
        let key: Buffer;
        try {
            cert = await readFileAsync(`${directoryPath}/${merchantCode}.cert`);
            key = await readFileAsync(`${directoryPath}/${merchantCode}.key`);
        } catch (err) {
            if (logErrors) {
                log.debug(err,
                    `Error when trying to read certificate from file for merchant with code ${merchantCode}`);
                // Cache an empty object in order to not log this error again on every request
                cache.set(merchantCode, {});
            }
        }
        return { cert, key };
    }

    public async getCertificate(merchant: MerchantInfo,
                                directoryPath: string = config.certDirectoryPath): Promise<SeamlessCertificate> {

        const cachedCertificate = cache.get(merchant.code);
        if (cachedCertificate && Object.keys(cachedCertificate).length) {
            log.info(getSecuredObjectData(cachedCertificate), "Using cached certificate");
            return cachedCertificate as SeamlessCertificate;
        }

        const { cert, key } = await this.readCertAndKeyFiles(
            directoryPath,
            merchant.code,
            cachedCertificate === undefined);

        const certificate: SeamlessCertificate = {
            ...await this.getPasswordAndCA(directoryPath, merchant.code, cachedCertificate === undefined)
        };
        if (cert) {
            certificate.cert = cert.toString();
        }

        if (key) {
            certificate.key = key.toString();
        }

        log.info(getSecuredObjectData(certificate), "Using certificate read from files");
        cache.set(merchant.code, certificate);

        return certificate;
    }
}

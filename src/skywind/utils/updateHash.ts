import * as crypto from "crypto";

export function updateHash(data: object, secret: string): string {
    const hashString: string = Object.keys(data).sort().reduce( (result: string[], key: string) => {
        if (data[key]) {
            result.push(`${key}=${data[key]}`);
        }
        return result;
    }, []).join("&");

    return crypto.createHash("md5").update(hashString + secret).digest("hex");
}

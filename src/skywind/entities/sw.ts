export interface GameInfo {
    gameCode: string;
    providerCode: string;
    providerGameCode: string;
}

export interface BonusRequest {
    bonus?: boolean;
}

export interface PromoRequest {
    typePromo?: string;
}

export interface DebitContributionDetails {
    jackpot_id: string;
    pools: {
        pool_id: string;
        is_global_pool: boolean;
        jp_contribution: number;
    }[];
    jp_contribution: number;
}

export interface JackpotWinDetails {
    jackpot_id: string;
    pools: {
        pool_id: string;
        win: number;
    }[];
    jp_win: number;
}

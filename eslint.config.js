"use strict";
/** @type {import('eslint').Linter.FlatConfig[]} */
const typescriptEslint = require("@typescript-eslint/eslint-plugin");
const typescriptParser = require("@typescript-eslint/parser");

module.exports = [
    {
        ignores: [
            "out/*",
            "out/**/*",
            "eslint.config.js",
            "node_modules/*",
            "node_modules/**/*",
        ],
        files: ["**/*.ts"],
        languageOptions: {
            ecmaVersion: 2022,
            sourceType: "module",
            parser: typescriptParser,
            parserOptions: {
                tsconfigRootDir: __dirname,
                project: ["./tsconfig.json"],
            },
        },
        plugins: {
            "@typescript-eslint": typescriptEslint,
        },
        rules: {
            "semi": ["error"],
            "arrow-spacing": "error",
            "no-console": ["error"],
            /*"@typescript-eslint/ban-ts-comment": "off",
            "@typescript-eslint/no-explicit-any": "off",
            "@typescript-eslint/no-unused-vars": "off",
            "@typescript-eslint/no-var-requires": "off",
            "no-prototype-builtins": "off",*/
            "quotes": ["error", "double"]
        }
    },
]
